# Advanced Planning & Memory Management

This module demonstrates advanced Semantic Kernel capabilities including custom planning, memory management, context handling, and workflow orchestration. These features enable sophisticated AI agent behaviors and complex task automation.

## 🎯 What You'll Learn

- Custom planning algorithms and execution strategies
- Memory management and conversation context
- Context handling across different scopes
- Workflow orchestration with conditional logic
- Error handling and retry mechanisms
- State management in AI applications

## 🚀 Getting Started

### Prerequisites

- .NET 8.0 SDK
- OpenAI API key or Azure OpenAI service
- Completed previous modules (01-basics through 03-orchestration)

### Setup

1. **Configure your AI service** in `appsettings.json`
2. **Run the demo**:
   ```bash
   cd PlanningDemo
   dotnet run
   ```

## 🧠 Core Components

### 1. Custom Planner
**Purpose**: Create and execute multi-step plans automatically
**Features**:
- Goal decomposition into actionable steps
- Function discovery and selection
- Plan execution with context management
- Error handling and recovery

**Example Usage**:
```csharp
var planner = new CustomPlanner(kernel);
var goal = "Research AI trends, create a summary, and email the team";
var plan = await planner.CreatePlanAsync(goal);
var result = await planner.ExecutePlanAsync(plan);
```

### 2. Conversation Memory
**Purpose**: Maintain context and history across interactions
**Features**:
- Message storage with metadata
- Semantic relevance matching
- Context retrieval and formatting
- Session data management

**Example Usage**:
```csharp
var memory = new ConversationMemory();
await memory.StoreMessageAsync("user", "I'm working on a ML project");
var context = await memory.GetRelevantContextAsync("What was my project about?");
```

### 3. Context Manager
**Purpose**: Handle different scopes of context and state
**Features**:
- Multi-scope context (Global, Session, Temporary)
- Context snapshots and restoration
- Expiring context values
- Contextual prompt building

**Example Usage**:
```csharp
var contextManager = new ContextManager();
contextManager.SetContext("user_name", "Alice");
var prompt = contextManager.BuildContextualPrompt("Create a project plan");
```

### 4. Workflow Orchestrator
**Purpose**: Execute complex workflows with conditional logic
**Features**:
- Multi-step workflow execution
- Conditional branching
- Error handling strategies
- Retry mechanisms

**Example Usage**:
```csharp
var orchestrator = new WorkflowOrchestrator(kernel);
var workflow = new WorkflowDefinition { /* steps */ };
var result = await orchestrator.ExecuteWorkflowAsync(workflow, input);
```

## 📋 Planning Capabilities

### Automatic Plan Generation
The custom planner analyzes goals and available functions to create execution plans:

```csharp
// Goal: "Research AI trends and create a report"
// Generated Plan:
// 1. Use Web.FetchUrl to gather information
// 2. Use ResearchAgent to analyze findings
// 3. Use File.WriteFile to save report
// 4. Use Email.SendEmail to distribute
```

### Plan Execution
Plans are executed step-by-step with context preservation:
- Each step's output becomes input for subsequent steps
- Context is maintained throughout execution
- Errors are handled according to configured strategies

### Function Discovery
The planner automatically discovers available functions:
- Scans all registered plugins
- Analyzes function descriptions and parameters
- Selects appropriate functions for each step

## 🧠 Memory Management

### Conversation History
- Stores all interactions with timestamps
- Extracts metadata (topics, entities, sentiment)
- Maintains configurable history limits
- Provides semantic search capabilities

### Context Retrieval
- Finds relevant previous conversations
- Combines recent and semantically similar content
- Formats context for AI consumption
- Supports custom relevance scoring

### Session Data
- Tracks conversation statistics
- Stores user preferences and settings
- Maintains topic frequencies
- Provides session summaries

## 🔄 Context Handling

### Context Scopes
1. **Global**: Persistent across all sessions
2. **Session**: Maintained during a conversation
3. **Temporary**: Short-lived, task-specific context

### Context Operations
```csharp
// Set context at different scopes
contextManager.SetContext("user_role", "developer", ContextScope.Global);
contextManager.SetContext("current_task", "code_review", ContextScope.Session);
contextManager.SetContext("temp_data", data, ContextScope.Temporary);

// Build contextual prompts
var prompt = contextManager.BuildContextualPrompt("Analyze this code");
```

### Context Snapshots
- Save and restore context states
- Useful for branching conversations
- Enable context rollback capabilities

## 🔀 Workflow Orchestration

### Workflow Definition
```csharp
var workflow = new WorkflowDefinition
{
    Name = "Content Creation",
    Steps = new[]
    {
        new WorkflowStep { Name = "Research", Function = "ResearchTopic" },
        new WorkflowStep { Name = "Outline", Function = "CreateOutline" },
        new WorkflowStep { Name = "Write", Function = "WriteContent" },
        new WorkflowStep { Name = "Review", Function = "ReviewContent" }
    }
};
```

### Conditional Logic
```csharp
new WorkflowStep 
{ 
    Name = "Quality Check",
    Function = "CheckQuality",
    Condition = "quality_score > 8",
    OnError = ErrorHandling.Retry,
    RetryCount = 3
}
```

### Error Handling Strategies
- **Stop**: Halt execution on error
- **Continue**: Skip failed step and continue
- **Retry**: Attempt step multiple times

## 🎨 Advanced Patterns

### Plan Composition
```csharp
// Combine multiple plans
var researchPlan = await planner.CreatePlanAsync("Research topic X");
var analysisPlan = await planner.CreatePlanAsync("Analyze research results");
var combinedPlan = PlanComposer.Combine(researchPlan, analysisPlan);
```

### Memory-Driven Planning
```csharp
// Use conversation history to inform planning
var context = await memory.GetRelevantContextAsync(goal);
var contextualPlan = await planner.CreatePlanAsync(goal, context);
```

### Adaptive Workflows
```csharp
// Modify workflow based on intermediate results
workflow.Steps[2].Condition = "previous_step_confidence > 0.8";
workflow.Steps[3].OnError = ErrorHandling.Retry;
```

## 🎓 Learning Exercises

1. **Create a custom planner** for a specific domain (e.g., software development)
2. **Implement semantic memory** using embeddings for better context retrieval
3. **Build adaptive workflows** that modify themselves based on results
4. **Add plan optimization** to minimize execution time or resource usage
5. **Implement plan caching** to reuse successful plans
6. **Create workflow templates** for common task patterns

## 🔧 Configuration Options

### Memory Settings
```csharp
var memory = new ConversationMemory(maxHistorySize: 100);
memory.SetRelevanceThreshold(0.7);
memory.EnableSemanticSearch(true);
```

### Context Settings
```csharp
var contextManager = new ContextManager();
contextManager.SetContextWithExpiry("temp_token", token, TimeSpan.FromHours(1));
contextManager.EnableAutoCleanup(true);
```

### Workflow Settings
```csharp
var orchestrator = new WorkflowOrchestrator(kernel);
orchestrator.SetDefaultErrorHandling(ErrorHandling.Retry);
orchestrator.SetMaxRetryCount(3);
```

## 🛠️ Best Practices

1. **Plan Granularity**: Balance between too many small steps and too few large ones
2. **Context Management**: Clean up temporary context regularly
3. **Memory Limits**: Set appropriate history limits for your use case
4. **Error Handling**: Always define error handling strategies
5. **Performance**: Cache plans and context when possible
6. **Testing**: Test workflows with various input scenarios
7. **Monitoring**: Track plan execution success rates

## 🔗 Integration Patterns

### With Single Agents
```csharp
var agent = new ResearchAgent(kernel);
var planner = new CustomPlanner(kernel);

// Agent creates initial research
var research = await agent.ConductResearchAsync(topic);

// Planner creates follow-up plan
var plan = await planner.CreatePlanAsync($"Analyze and expand on: {research}");
```

### With Multi-Agent Systems
```csharp
// Coordinate multiple agents through planning
var plan = await planner.CreatePlanAsync("Complete software project");
// Plan automatically assigns tasks to appropriate agents
```

## 🔗 Next Steps

- **05-examples/**: See real-world applications of these concepts
- **Multi-Agent Systems**: Explore agent coordination patterns
- **Production Deployment**: Learn about scaling and monitoring

## 📖 Additional Resources

- [Semantic Kernel Planning Documentation](https://learn.microsoft.com/en-us/semantic-kernel/ai-orchestration/planners/)
- [AI Agent Memory Patterns](https://learn.microsoft.com/en-us/azure/ai-services/agents/concepts/memory)
- [Workflow Orchestration Best Practices](https://learn.microsoft.com/en-us/azure/architecture/ai-ml/guide/workflow-orchestration)
