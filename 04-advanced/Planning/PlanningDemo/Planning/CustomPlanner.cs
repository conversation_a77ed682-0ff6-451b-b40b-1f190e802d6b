using Microsoft.SemanticKernel;
using System.Text.Json;

namespace PlanningDemo.Planning;

/// <summary>
/// A custom planner that creates and executes multi-step plans.
/// </summary>
public class CustomPlanner
{
    private readonly Kernel _kernel;
    private readonly KernelFunction _planCreationFunction;
    private readonly KernelFunction _stepExecutionFunction;

    public CustomPlanner(Kernel kernel)
    {
        _kernel = kernel;
        _planCreationFunction = CreatePlanCreationFunction();
        _stepExecutionFunction = CreateStepExecutionFunction();
    }

    public async Task<Plan> CreatePlanAsync(string goal)
    {
        var availableFunctions = GetAvailableFunctions();
        
        var result = await _kernel.InvokeAsync(_planCreationFunction, new()
        {
            ["goal"] = goal,
            ["availableFunctions"] = availableFunctions
        });

        return ParsePlan(result.ToString());
    }

    public async Task<string> ExecutePlanAsync(Plan plan)
    {
        var results = new List<string>();
        var context = new Dictionary<string, object>();

        Console.WriteLine($"Executing plan: {plan.Name}");
        Console.WriteLine($"Steps: {plan.Steps.Count}");

        foreach (var step in plan.Steps)
        {
            Console.WriteLine($"  Executing step: {step.Name}");
            
            try
            {
                var stepResult = await ExecuteStepAsync(step, context);
                results.Add($"Step '{step.Name}': {stepResult}");
                
                // Store result in context for future steps
                context[$"step_{step.Name.Replace(" ", "_").ToLower()}_result"] = stepResult;
                
                Console.WriteLine($"    ✓ Completed: {stepResult.Substring(0, Math.Min(100, stepResult.Length))}...");
            }
            catch (Exception ex)
            {
                var errorMsg = $"Step '{step.Name}' failed: {ex.Message}";
                results.Add(errorMsg);
                Console.WriteLine($"    ✗ Failed: {ex.Message}");
            }
        }

        return string.Join("\n\n", results);
    }

    private async Task<string> ExecuteStepAsync(PlanStep step, Dictionary<string, object> context)
    {
        // Build context string for the step
        var contextString = string.Join("\n", context.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
        
        var result = await _kernel.InvokeAsync(_stepExecutionFunction, new()
        {
            ["stepName"] = step.Name,
            ["stepDescription"] = step.Description,
            ["functionName"] = step.FunctionName,
            ["parameters"] = step.Parameters,
            ["context"] = contextString
        });

        return result.ToString();
    }

    private string GetAvailableFunctions()
    {
        var functions = new List<string>();
        
        foreach (var plugin in _kernel.Plugins)
        {
            foreach (var function in plugin)
            {
                functions.Add($"{plugin.Name}.{function.Name}: {function.Description}");
            }
        }

        return string.Join("\n", functions);
    }

    private Plan ParsePlan(string planJson)
    {
        try
        {
            // Try to parse as JSON first
            var planData = JsonSerializer.Deserialize<JsonElement>(planJson);
            
            var plan = new Plan
            {
                Name = planData.GetProperty("name").GetString() ?? "Generated Plan",
                Description = planData.GetProperty("description").GetString() ?? "",
                Steps = new List<PlanStep>()
            };

            if (planData.TryGetProperty("steps", out var stepsElement))
            {
                foreach (var stepElement in stepsElement.EnumerateArray())
                {
                    var step = new PlanStep
                    {
                        Name = stepElement.GetProperty("name").GetString() ?? "",
                        Description = stepElement.GetProperty("description").GetString() ?? "",
                        FunctionName = stepElement.TryGetProperty("function", out var funcElement) ? funcElement.GetString() ?? "" : "",
                        Parameters = stepElement.TryGetProperty("parameters", out var paramElement) ? paramElement.GetString() ?? "" : ""
                    };
                    plan.Steps.Add(step);
                }
            }

            return plan;
        }
        catch
        {
            // Fallback: parse as text and create a simple plan
            return CreateFallbackPlan(planJson);
        }
    }

    private Plan CreateFallbackPlan(string planText)
    {
        var lines = planText.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var plan = new Plan
        {
            Name = "Generated Plan",
            Description = "Plan created from text description",
            Steps = new List<PlanStep>()
        };

        for (int i = 0; i < lines.Length; i++)
        {
            var line = lines[i].Trim();
            if (line.StartsWith("Step") || line.Contains("."))
            {
                plan.Steps.Add(new PlanStep
                {
                    Name = $"Step {plan.Steps.Count + 1}",
                    Description = line,
                    FunctionName = "GeneralTask",
                    Parameters = line
                });
            }
        }

        return plan;
    }

    private KernelFunction CreatePlanCreationFunction()
    {
        var prompt = """
            You are an expert planner. Create a detailed execution plan for the following goal:

            Goal: {{$goal}}

            Available functions:
            {{$availableFunctions}}

            Create a plan with the following JSON structure:
            {
              "name": "Plan Name",
              "description": "Plan description",
              "steps": [
                {
                  "name": "Step Name",
                  "description": "What this step accomplishes",
                  "function": "FunctionName (if applicable)",
                  "parameters": "Parameters for the function"
                }
              ]
            }

            Make sure the plan is:
            1. Logical and sequential
            2. Uses available functions when possible
            3. Breaks down complex tasks into manageable steps
            4. Includes error handling considerations
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "CreatePlan");
    }

    private KernelFunction CreateStepExecutionFunction()
    {
        var prompt = """
            Execute the following plan step:

            Step Name: {{$stepName}}
            Description: {{$stepDescription}}
            Function: {{$functionName}}
            Parameters: {{$parameters}}

            Context from previous steps:
            {{$context}}

            Provide a detailed result of executing this step. If this step involves calling a function,
            simulate the function call and provide realistic output. If it's a general task,
            provide a comprehensive response that accomplishes the step's objective.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "ExecuteStep");
    }
}

public class Plan
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public List<PlanStep> Steps { get; set; } = new();
}

public class PlanStep
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string FunctionName { get; set; } = "";
    public string Parameters { get; set; } = "";
}
