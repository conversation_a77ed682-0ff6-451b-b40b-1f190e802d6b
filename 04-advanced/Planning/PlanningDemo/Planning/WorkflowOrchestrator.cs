using Microsoft.SemanticKernel;

namespace PlanningDemo.Planning;

/// <summary>
/// Orchestrates complex workflows with multiple steps and conditional logic.
/// </summary>
public class WorkflowOrchestrator
{
    private readonly Kernel _kernel;
    private readonly Dictionary<string, KernelFunction> _workflowFunctions;

    public WorkflowOrchestrator(Kernel kernel)
    {
        _kernel = kernel;
        _workflowFunctions = new Dictionary<string, KernelFunction>();
        InitializeWorkflowFunctions();
    }

    public async Task<string> ExecuteWorkflowAsync(WorkflowDefinition workflow, string input)
    {
        var context = new WorkflowContext
        {
            Input = input,
            Variables = new Dictionary<string, object>(),
            Results = new Dictionary<string, string>()
        };

        Console.WriteLine($"Starting workflow: {workflow.Name}");
        
        foreach (var step in workflow.Steps)
        {
            try
            {
                Console.WriteLine($"  Executing step: {step.Name}");
                
                var stepResult = await ExecuteWorkflowStepAsync(step, context);
                context.Results[step.Name] = stepResult;
                context.Variables[$"step_{step.Name.Replace(" ", "_").ToLower()}_result"] = stepResult;
                
                Console.WriteLine($"    ✓ Completed: {stepResult.Substring(0, Math.Min(100, stepResult.Length))}...");
                
                // Check for conditional logic
                if (!string.IsNullOrEmpty(step.Condition))
                {
                    var conditionMet = await EvaluateConditionAsync(step.Condition, context);
                    if (!conditionMet)
                    {
                        Console.WriteLine($"    ⏭️ Skipping remaining steps due to condition: {step.Condition}");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ✗ Step failed: {ex.Message}");
                
                if (step.OnError == ErrorHandling.Stop)
                {
                    throw;
                }
                else if (step.OnError == ErrorHandling.Continue)
                {
                    context.Results[step.Name] = $"Error: {ex.Message}";
                    continue;
                }
                else if (step.OnError == ErrorHandling.Retry && step.RetryCount > 0)
                {
                    for (int retry = 0; retry < step.RetryCount; retry++)
                    {
                        try
                        {
                            Console.WriteLine($"    🔄 Retrying step {step.Name} (attempt {retry + 1})");
                            var retryResult = await ExecuteWorkflowStepAsync(step, context);
                            context.Results[step.Name] = retryResult;
                            break;
                        }
                        catch (Exception retryEx)
                        {
                            if (retry == step.RetryCount - 1)
                            {
                                context.Results[step.Name] = $"Error after {step.RetryCount} retries: {retryEx.Message}";
                            }
                        }
                    }
                }
            }
        }

        return FormatWorkflowResults(workflow, context);
    }

    private async Task<string> ExecuteWorkflowStepAsync(WorkflowStep step, WorkflowContext context)
    {
        if (_workflowFunctions.TryGetValue(step.Function, out var function))
        {
            var parameters = BuildStepParameters(step, context);
            var result = await _kernel.InvokeAsync(function, parameters);
            return result.ToString();
        }
        else
        {
            // Generic step execution
            var genericFunction = _workflowFunctions["GenericStep"];
            var parameters = new KernelArguments
            {
                ["stepName"] = step.Name,
                ["stepDescription"] = step.Description,
                ["input"] = context.Input,
                ["context"] = FormatContext(context)
            };
            
            var result = await _kernel.InvokeAsync(genericFunction, parameters);
            return result.ToString();
        }
    }

    private KernelArguments BuildStepParameters(WorkflowStep step, WorkflowContext context)
    {
        var parameters = new KernelArguments
        {
            ["input"] = context.Input,
            ["context"] = FormatContext(context)
        };

        // Add step-specific parameters
        if (!string.IsNullOrEmpty(step.Parameters))
        {
            var stepParams = step.Parameters.Split(',');
            foreach (var param in stepParams)
            {
                var parts = param.Split('=');
                if (parts.Length == 2)
                {
                    parameters[parts[0].Trim()] = parts[1].Trim();
                }
            }
        }

        return parameters;
    }

    private async Task<bool> EvaluateConditionAsync(string condition, WorkflowContext context)
    {
        var evaluationFunction = _workflowFunctions["EvaluateCondition"];
        var result = await _kernel.InvokeAsync(evaluationFunction, new KernelArguments
        {
            ["condition"] = condition,
            ["context"] = FormatContext(context)
        });

        return result.ToString().ToLower().Contains("true");
    }

    private string FormatContext(WorkflowContext context)
    {
        var contextLines = new List<string>
        {
            $"Input: {context.Input}"
        };

        if (context.Variables.Any())
        {
            contextLines.Add("Variables:");
            foreach (var kvp in context.Variables)
            {
                contextLines.Add($"  {kvp.Key}: {kvp.Value}");
            }
        }

        if (context.Results.Any())
        {
            contextLines.Add("Previous Results:");
            foreach (var kvp in context.Results)
            {
                contextLines.Add($"  {kvp.Key}: {kvp.Value}");
            }
        }

        return string.Join("\n", contextLines);
    }

    private string FormatWorkflowResults(WorkflowDefinition workflow, WorkflowContext context)
    {
        var resultLines = new List<string>
        {
            $"Workflow '{workflow.Name}' completed successfully.",
            "",
            "Step Results:"
        };

        foreach (var kvp in context.Results)
        {
            resultLines.Add($"  {kvp.Key}: {kvp.Value}");
        }

        return string.Join("\n", resultLines);
    }

    private void InitializeWorkflowFunctions()
    {
        // Research Topic Function
        _workflowFunctions["ResearchTopic"] = _kernel.CreateFunctionFromPrompt(
            """
            Research the following topic thoroughly:
            
            Topic: {{$input}}
            
            Provide:
            1. Overview of the topic
            2. Key points and facts
            3. Current trends
            4. Important considerations
            5. Relevant statistics or data
            
            Context: {{$context}}
            """, "ResearchTopic");

        // Create Outline Function
        _workflowFunctions["CreateOutline"] = _kernel.CreateFunctionFromPrompt(
            """
            Create a detailed outline for content about:
            
            Topic: {{$input}}
            
            Based on the research context:
            {{$context}}
            
            Provide a structured outline with:
            1. Introduction
            2. Main sections with subsections
            3. Key points for each section
            4. Conclusion
            """, "CreateOutline");

        // Write Content Function
        _workflowFunctions["WriteContent"] = _kernel.CreateFunctionFromPrompt(
            """
            Write comprehensive content based on:
            
            Topic: {{$input}}
            
            Using the outline and research from context:
            {{$context}}
            
            Create well-structured, informative content that:
            1. Follows the outline
            2. Incorporates research findings
            3. Is engaging and readable
            4. Includes relevant examples
            """, "WriteContent");

        // Review Content Function
        _workflowFunctions["ReviewContent"] = _kernel.CreateFunctionFromPrompt(
            """
            Review and improve the following content:
            
            Original topic: {{$input}}
            
            Content and context:
            {{$context}}
            
            Provide:
            1. Quality assessment
            2. Suggestions for improvement
            3. Fact-checking notes
            4. Revised version if needed
            """, "ReviewContent");

        // Finalize Content Function
        _workflowFunctions["FinalizeContent"] = _kernel.CreateFunctionFromPrompt(
            """
            Finalize the content for publication:
            
            Topic: {{$input}}
            
            Based on all previous work:
            {{$context}}
            
            Provide the final, polished version with:
            1. Proper formatting
            2. Final edits applied
            3. Publication-ready content
            4. Metadata (title, tags, summary)
            """, "FinalizeContent");

        // Generic Step Function
        _workflowFunctions["GenericStep"] = _kernel.CreateFunctionFromPrompt(
            """
            Execute the following workflow step:
            
            Step: {{$stepName}}
            Description: {{$stepDescription}}
            Input: {{$input}}
            
            Context from previous steps:
            {{$context}}
            
            Provide a comprehensive result that accomplishes the step's objective.
            """, "GenericStep");

        // Condition Evaluation Function
        _workflowFunctions["EvaluateCondition"] = _kernel.CreateFunctionFromPrompt(
            """
            Evaluate the following condition:
            
            Condition: {{$condition}}
            
            Based on the context:
            {{$context}}
            
            Return "true" if the condition is met, "false" otherwise.
            Provide a brief explanation of your evaluation.
            """, "EvaluateCondition");
    }
}

public class WorkflowDefinition
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public WorkflowStep[] Steps { get; set; } = Array.Empty<WorkflowStep>();
}

public class WorkflowStep
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string Function { get; set; } = "";
    public string Parameters { get; set; } = "";
    public string Condition { get; set; } = "";
    public ErrorHandling OnError { get; set; } = ErrorHandling.Stop;
    public int RetryCount { get; set; } = 0;
}

public class WorkflowContext
{
    public string Input { get; set; } = "";
    public Dictionary<string, object> Variables { get; set; } = new();
    public Dictionary<string, string> Results { get; set; } = new();
}

public enum ErrorHandling
{
    Stop,
    Continue,
    Retry
}
