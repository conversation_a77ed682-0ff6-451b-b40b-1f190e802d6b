using System.Text.Json;

namespace PlanningDemo.Memory;

/// <summary>
/// Manages conversation memory and context for maintaining state across interactions.
/// </summary>
public class ConversationMemory
{
    private readonly List<ConversationEntry> _conversationHistory;
    private readonly Dictionary<string, object> _sessionData;
    private readonly int _maxHistorySize;

    public ConversationMemory(int maxHistorySize = 50)
    {
        _conversationHistory = new List<ConversationEntry>();
        _sessionData = new Dictionary<string, object>();
        _maxHistorySize = maxHistorySize;
    }

    public async Task StoreMessageAsync(string role, string message)
    {
        var entry = new ConversationEntry
        {
            Id = Guid.NewGuid().ToString(),
            Role = role,
            Message = message,
            Timestamp = DateTime.UtcNow,
            Metadata = ExtractMetadata(message)
        };

        _conversationHistory.Add(entry);

        // Maintain history size limit
        if (_conversationHistory.Count > _maxHistorySize)
        {
            _conversationHistory.RemoveAt(0);
        }

        // Update session data based on message content
        await UpdateSessionDataAsync(entry);
    }

    public async Task<string> GetRelevantContextAsync(string currentMessage, int maxEntries = 5)
    {
        // Get recent conversation history
        var recentEntries = _conversationHistory
            .TakeLast(maxEntries)
            .ToList();

        // Find semantically relevant entries
        var relevantEntries = await FindRelevantEntriesAsync(currentMessage, maxEntries);

        // Combine recent and relevant entries
        var contextEntries = recentEntries
            .Union(relevantEntries)
            .DistinctBy(e => e.Id)
            .OrderBy(e => e.Timestamp)
            .ToList();

        return FormatContext(contextEntries);
    }

    public void SetSessionData(string key, object value)
    {
        _sessionData[key] = value;
    }

    public T? GetSessionData<T>(string key)
    {
        if (_sessionData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default(T);
    }

    public string GetSessionSummary()
    {
        var summary = new
        {
            TotalMessages = _conversationHistory.Count,
            SessionData = _sessionData,
            Topics = GetDiscussedTopics(),
            TimeSpan = GetConversationTimeSpan()
        };

        return JsonSerializer.Serialize(summary, new JsonSerializerOptions { WriteIndented = true });
    }

    public void ClearHistory()
    {
        _conversationHistory.Clear();
    }

    public void ClearSessionData()
    {
        _sessionData.Clear();
    }

    private async Task<List<ConversationEntry>> FindRelevantEntriesAsync(string currentMessage, int maxEntries)
    {
        // Simple keyword-based relevance (in a real implementation, you'd use embeddings)
        var keywords = ExtractKeywords(currentMessage);
        
        var relevantEntries = _conversationHistory
            .Where(entry => keywords.Any(keyword => 
                entry.Message.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                entry.Metadata.ContainsKey("topics") && 
                entry.Metadata["topics"].ToString()!.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            .OrderByDescending(entry => CalculateRelevanceScore(entry, keywords))
            .Take(maxEntries)
            .ToList();

        return relevantEntries;
    }

    private Dictionary<string, object> ExtractMetadata(string message)
    {
        var metadata = new Dictionary<string, object>();
        
        // Extract topics (simple keyword extraction)
        var topics = ExtractKeywords(message);
        metadata["topics"] = string.Join(", ", topics);
        
        // Extract entities (simple pattern matching)
        var entities = ExtractEntities(message);
        if (entities.Any())
        {
            metadata["entities"] = entities;
        }
        
        // Sentiment analysis (simple)
        metadata["sentiment"] = AnalyzeSentiment(message);
        
        return metadata;
    }

    private List<string> ExtractKeywords(string text)
    {
        var commonWords = new HashSet<string> { "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "i", "you", "he", "she", "it", "we", "they", "this", "that", "these", "those" };
        
        return text.ToLower()
            .Split(new char[] { ' ', '.', ',', '!', '?', ';', ':', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
            .Where(word => word.Length > 2 && !commonWords.Contains(word))
            .Distinct()
            .ToList();
    }

    private List<string> ExtractEntities(string text)
    {
        var entities = new List<string>();
        
        // Simple pattern matching for common entities
        var patterns = new Dictionary<string, string>
        {
            { "email", @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b" },
            { "url", @"https?://[^\s]+" },
            { "phone", @"\b\d{3}-\d{3}-\d{4}\b" }
        };

        foreach (var pattern in patterns)
        {
            var matches = System.Text.RegularExpressions.Regex.Matches(text, pattern.Value);
            entities.AddRange(matches.Select(m => m.Value));
        }

        return entities;
    }

    private string AnalyzeSentiment(string text)
    {
        var positiveWords = new[] { "good", "great", "excellent", "amazing", "wonderful", "fantastic", "love", "like", "happy", "pleased" };
        var negativeWords = new[] { "bad", "terrible", "awful", "horrible", "hate", "dislike", "angry", "frustrated", "disappointed", "sad" };

        var words = text.ToLower().Split(' ');
        var positiveCount = words.Count(w => positiveWords.Contains(w));
        var negativeCount = words.Count(w => negativeWords.Contains(w));

        if (positiveCount > negativeCount) return "positive";
        if (negativeCount > positiveCount) return "negative";
        return "neutral";
    }

    private double CalculateRelevanceScore(ConversationEntry entry, List<string> keywords)
    {
        var score = 0.0;
        var entryWords = entry.Message.ToLower().Split(' ');
        
        foreach (var keyword in keywords)
        {
            if (entryWords.Contains(keyword.ToLower()))
            {
                score += 1.0;
            }
        }

        // Boost score for recent messages
        var hoursSinceMessage = (DateTime.UtcNow - entry.Timestamp).TotalHours;
        var recencyBoost = Math.Max(0, 1.0 - (hoursSinceMessage / 24.0));
        
        return score + recencyBoost;
    }

    private string FormatContext(List<ConversationEntry> entries)
    {
        if (!entries.Any())
            return "No relevant context found.";

        var contextLines = entries.Select(entry => 
            $"[{entry.Timestamp:HH:mm}] {entry.Role}: {entry.Message}");
        
        return string.Join("\n", contextLines);
    }

    private async Task UpdateSessionDataAsync(ConversationEntry entry)
    {
        // Update session data based on conversation content
        if (entry.Metadata.ContainsKey("topics"))
        {
            var topics = entry.Metadata["topics"].ToString()!.Split(", ");
            foreach (var topic in topics)
            {
                var key = $"topic_{topic}";
                var count = GetSessionData<int>(key);
                SetSessionData(key, count + 1);
            }
        }

        // Track conversation length
        var messageCount = GetSessionData<int>("message_count");
        SetSessionData("message_count", messageCount + 1);

        // Update last activity
        SetSessionData("last_activity", DateTime.UtcNow);
    }

    private List<string> GetDiscussedTopics()
    {
        return _sessionData.Keys
            .Where(key => key.StartsWith("topic_"))
            .Select(key => key.Substring(6))
            .ToList();
    }

    private string GetConversationTimeSpan()
    {
        if (!_conversationHistory.Any())
            return "No conversation";

        var start = _conversationHistory.First().Timestamp;
        var end = _conversationHistory.Last().Timestamp;
        var duration = end - start;

        return $"{duration.TotalMinutes:F1} minutes";
    }
}

public class ConversationEntry
{
    public string Id { get; set; } = "";
    public string Role { get; set; } = "";
    public string Message { get; set; } = "";
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
