namespace PlanningDemo.Memory;

/// <summary>
/// Manages context and state across different operations and conversations.
/// </summary>
public class ContextManager
{
    private readonly Dictionary<string, object> _globalContext;
    private readonly Dictionary<string, object> _sessionContext;
    private readonly Dictionary<string, object> _temporaryContext;
    private readonly Stack<Dictionary<string, object>> _contextStack;

    public ContextManager()
    {
        _globalContext = new Dictionary<string, object>();
        _sessionContext = new Dictionary<string, object>();
        _temporaryContext = new Dictionary<string, object>();
        _contextStack = new Stack<Dictionary<string, object>>();
    }

    public void SetContext(string key, object value, ContextScope scope = ContextScope.Session)
    {
        var targetContext = GetContextDictionary(scope);
        targetContext[key] = value;
    }

    public T? GetContext<T>(string key, ContextScope scope = ContextScope.Session)
    {
        var targetContext = GetContextDictionary(scope);
        if (targetContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default(T);
    }

    public void UpdateContext(string key, object value, ContextScope scope = ContextScope.Session)
    {
        SetContext(key, value, scope);
    }

    public void RemoveContext(string key, ContextScope scope = ContextScope.Session)
    {
        var targetContext = GetContextDictionary(scope);
        targetContext.Remove(key);
    }

    public void ClearContext(ContextScope scope = ContextScope.Temporary)
    {
        var targetContext = GetContextDictionary(scope);
        targetContext.Clear();
    }

    public void PushContext()
    {
        // Save current session context to stack
        var contextSnapshot = new Dictionary<string, object>(_sessionContext);
        _contextStack.Push(contextSnapshot);
    }

    public void PopContext()
    {
        if (_contextStack.Count > 0)
        {
            var previousContext = _contextStack.Pop();
            _sessionContext.Clear();
            foreach (var kvp in previousContext)
            {
                _sessionContext[kvp.Key] = kvp.Value;
            }
        }
    }

    public string BuildContextualPrompt(string basePrompt)
    {
        var contextInfo = GetContextSummary();
        
        if (string.IsNullOrEmpty(contextInfo))
        {
            return basePrompt;
        }

        return $"""
            Context Information:
            {contextInfo}

            Task: {basePrompt}

            Please consider the context information when responding to the task.
            """;
    }

    public string GetContextSummary()
    {
        var summaryParts = new List<string>();

        if (_globalContext.Any())
        {
            summaryParts.Add("Global Context:");
            foreach (var kvp in _globalContext)
            {
                summaryParts.Add($"  {kvp.Key}: {kvp.Value}");
            }
        }

        if (_sessionContext.Any())
        {
            summaryParts.Add("Session Context:");
            foreach (var kvp in _sessionContext)
            {
                summaryParts.Add($"  {kvp.Key}: {kvp.Value}");
            }
        }

        if (_temporaryContext.Any())
        {
            summaryParts.Add("Temporary Context:");
            foreach (var kvp in _temporaryContext)
            {
                summaryParts.Add($"  {kvp.Key}: {kvp.Value}");
            }
        }

        return string.Join("\n", summaryParts);
    }

    public Dictionary<string, object> GetAllContext()
    {
        var allContext = new Dictionary<string, object>();

        // Add contexts in order of precedence (temporary overrides session overrides global)
        foreach (var kvp in _globalContext)
        {
            allContext[kvp.Key] = kvp.Value;
        }

        foreach (var kvp in _sessionContext)
        {
            allContext[kvp.Key] = kvp.Value;
        }

        foreach (var kvp in _temporaryContext)
        {
            allContext[kvp.Key] = kvp.Value;
        }

        return allContext;
    }

    public void MergeContext(Dictionary<string, object> externalContext, ContextScope scope = ContextScope.Temporary)
    {
        var targetContext = GetContextDictionary(scope);
        foreach (var kvp in externalContext)
        {
            targetContext[kvp.Key] = kvp.Value;
        }
    }

    public ContextSnapshot CreateSnapshot()
    {
        return new ContextSnapshot
        {
            GlobalContext = new Dictionary<string, object>(_globalContext),
            SessionContext = new Dictionary<string, object>(_sessionContext),
            TemporaryContext = new Dictionary<string, object>(_temporaryContext),
            Timestamp = DateTime.UtcNow
        };
    }

    public void RestoreSnapshot(ContextSnapshot snapshot)
    {
        _globalContext.Clear();
        _sessionContext.Clear();
        _temporaryContext.Clear();

        foreach (var kvp in snapshot.GlobalContext)
        {
            _globalContext[kvp.Key] = kvp.Value;
        }

        foreach (var kvp in snapshot.SessionContext)
        {
            _sessionContext[kvp.Key] = kvp.Value;
        }

        foreach (var kvp in snapshot.TemporaryContext)
        {
            _temporaryContext[kvp.Key] = kvp.Value;
        }
    }

    public bool HasContext(string key, ContextScope? scope = null)
    {
        if (scope.HasValue)
        {
            var targetContext = GetContextDictionary(scope.Value);
            return targetContext.ContainsKey(key);
        }

        // Check all scopes
        return _globalContext.ContainsKey(key) ||
               _sessionContext.ContainsKey(key) ||
               _temporaryContext.ContainsKey(key);
    }

    public void SetContextWithExpiry(string key, object value, TimeSpan expiry, ContextScope scope = ContextScope.Temporary)
    {
        var expiryTime = DateTime.UtcNow.Add(expiry);
        var wrappedValue = new ExpiringValue
        {
            Value = value,
            ExpiryTime = expiryTime
        };

        SetContext(key, wrappedValue, scope);
    }

    public void CleanupExpiredContext()
    {
        CleanupExpiredContextInDictionary(_globalContext);
        CleanupExpiredContextInDictionary(_sessionContext);
        CleanupExpiredContextInDictionary(_temporaryContext);
    }

    private void CleanupExpiredContextInDictionary(Dictionary<string, object> context)
    {
        var expiredKeys = new List<string>();
        var now = DateTime.UtcNow;

        foreach (var kvp in context)
        {
            if (kvp.Value is ExpiringValue expiringValue && expiringValue.ExpiryTime <= now)
            {
                expiredKeys.Add(kvp.Key);
            }
        }

        foreach (var key in expiredKeys)
        {
            context.Remove(key);
        }
    }

    private Dictionary<string, object> GetContextDictionary(ContextScope scope)
    {
        return scope switch
        {
            ContextScope.Global => _globalContext,
            ContextScope.Session => _sessionContext,
            ContextScope.Temporary => _temporaryContext,
            _ => _sessionContext
        };
    }
}

public enum ContextScope
{
    Global,
    Session,
    Temporary
}

public class ContextSnapshot
{
    public Dictionary<string, object> GlobalContext { get; set; } = new();
    public Dictionary<string, object> SessionContext { get; set; } = new();
    public Dictionary<string, object> TemporaryContext { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

public class ExpiringValue
{
    public object Value { get; set; } = null!;
    public DateTime ExpiryTime { get; set; }
}
