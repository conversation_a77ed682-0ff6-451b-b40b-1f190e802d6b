using System.ComponentModel;
using Microsoft.SemanticKernel;

namespace PlanningDemo;

/// <summary>
/// File operations plugin for planning examples.
/// </summary>
public class FilePlugin
{
    [KernelFunction]
    [Description("Reads content from a file")]
    public string ReadFile([Description("Path to the file")] string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                return File.ReadAllText(filePath);
            }
            return $"File not found: {filePath}";
        }
        catch (Exception ex)
        {
            return $"Error reading file: {ex.Message}";
        }
    }

    [KernelFunction]
    [Description("Writes content to a file")]
    public string WriteFile(
        [Description("Path to the file")] string filePath,
        [Description("Content to write")] string content)
    {
        try
        {
            File.WriteAllText(filePath, content);
            return $"Successfully wrote to {filePath}";
        }
        catch (Exception ex)
        {
            return $"Error writing file: {ex.Message}";
        }
    }

    [KernelFunction]
    [Description("Checks if a file exists")]
    public bool FileExists([Description("Path to the file")] string filePath)
    {
        return File.Exists(filePath);
    }

    [KernelFunction]
    [Description("Lists files in a directory")]
    public string ListFiles([Description("Directory path")] string directoryPath)
    {
        try
        {
            if (Directory.Exists(directoryPath))
            {
                var files = Directory.GetFiles(directoryPath);
                return string.Join("\n", files);
            }
            return $"Directory not found: {directoryPath}";
        }
        catch (Exception ex)
        {
            return $"Error listing files: {ex.Message}";
        }
    }
}

/// <summary>
/// Web operations plugin for planning examples.
/// </summary>
public class WebPlugin
{
    [KernelFunction]
    [Description("Simulates fetching content from a URL")]
    public string FetchUrl([Description("URL to fetch")] string url)
    {
        // Simulate web request
        return $"Simulated content from {url}: This is sample web content that would be fetched from the URL.";
    }

    [KernelFunction]
    [Description("Simulates posting data to a URL")]
    public string PostData(
        [Description("URL to post to")] string url,
        [Description("Data to post")] string data)
    {
        return $"Successfully posted data to {url}. Response: Data received and processed.";
    }

    [KernelFunction]
    [Description("Simulates checking if a URL is accessible")]
    public bool CheckUrl([Description("URL to check")] string url)
    {
        // Simulate URL check
        return url.StartsWith("http://") || url.StartsWith("https://");
    }

    [KernelFunction]
    [Description("Simulates downloading a file from a URL")]
    public string DownloadFile(
        [Description("URL of the file")] string url,
        [Description("Local path to save the file")] string localPath)
    {
        return $"Simulated download of {url} to {localPath}. File downloaded successfully.";
    }
}

/// <summary>
/// Email operations plugin for planning examples.
/// </summary>
public class EmailPlugin
{
    [KernelFunction]
    [Description("Simulates sending an email")]
    public string SendEmail(
        [Description("Recipient email address")] string to,
        [Description("Email subject")] string subject,
        [Description("Email body")] string body)
    {
        return $"Email sent successfully to {to} with subject '{subject}'. Body length: {body.Length} characters.";
    }

    [KernelFunction]
    [Description("Simulates sending an email with attachment")]
    public string SendEmailWithAttachment(
        [Description("Recipient email address")] string to,
        [Description("Email subject")] string subject,
        [Description("Email body")] string body,
        [Description("Attachment file path")] string attachmentPath)
    {
        return $"Email with attachment sent to {to}. Subject: '{subject}', Attachment: {attachmentPath}";
    }

    [KernelFunction]
    [Description("Simulates checking for new emails")]
    public string CheckEmails([Description("Email account")] string account)
    {
        return $"Checked emails for {account}. Found 3 new messages.";
    }

    [KernelFunction]
    [Description("Validates an email address format")]
    public bool ValidateEmail([Description("Email address to validate")] string email)
    {
        return email.Contains("@") && email.Contains(".");
    }
}

/// <summary>
/// Calculator plugin for planning examples.
/// </summary>
public class CalculatorPlugin
{
    [KernelFunction]
    [Description("Adds two numbers")]
    public double Add(
        [Description("First number")] double a,
        [Description("Second number")] double b)
    {
        return a + b;
    }

    [KernelFunction]
    [Description("Subtracts two numbers")]
    public double Subtract(
        [Description("First number")] double a,
        [Description("Second number")] double b)
    {
        return a - b;
    }

    [KernelFunction]
    [Description("Multiplies two numbers")]
    public double Multiply(
        [Description("First number")] double a,
        [Description("Second number")] double b)
    {
        return a * b;
    }

    [KernelFunction]
    [Description("Divides two numbers")]
    public double Divide(
        [Description("Dividend")] double a,
        [Description("Divisor")] double b)
    {
        if (b == 0)
            throw new ArgumentException("Cannot divide by zero");
        return a / b;
    }

    [KernelFunction]
    [Description("Calculates percentage")]
    public double Percentage(
        [Description("Value")] double value,
        [Description("Percentage")] double percentage)
    {
        return (value * percentage) / 100;
    }

    [KernelFunction]
    [Description("Calculates compound interest")]
    public double CompoundInterest(
        [Description("Principal amount")] double principal,
        [Description("Annual interest rate (as decimal)")] double rate,
        [Description("Number of years")] int years,
        [Description("Compounding frequency per year")] int compoundingFrequency = 1)
    {
        return principal * Math.Pow(1 + (rate / compoundingFrequency), compoundingFrequency * years);
    }

    [KernelFunction]
    [Description("Calculates the average of a list of numbers")]
    public double Average([Description("Comma-separated list of numbers")] string numbers)
    {
        var numberList = numbers.Split(',')
            .Select(n => double.Parse(n.Trim()))
            .ToList();
        
        return numberList.Average();
    }

    [KernelFunction]
    [Description("Finds the maximum value in a list of numbers")]
    public double Maximum([Description("Comma-separated list of numbers")] string numbers)
    {
        var numberList = numbers.Split(',')
            .Select(n => double.Parse(n.Trim()))
            .ToList();
        
        return numberList.Max();
    }

    [KernelFunction]
    [Description("Finds the minimum value in a list of numbers")]
    public double Minimum([Description("Comma-separated list of numbers")] string numbers)
    {
        var numberList = numbers.Split(',')
            .Select(n => double.Parse(n.Trim()))
            .ToList();
        
        return numberList.Min();
    }

    [KernelFunction]
    [Description("Calculates the square root of a number")]
    public double SquareRoot([Description("Number to calculate square root for")] double number)
    {
        if (number < 0)
            throw new ArgumentException("Cannot calculate square root of negative number");
        return Math.Sqrt(number);
    }

    [KernelFunction]
    [Description("Calculates power of a number")]
    public double Power(
        [Description("Base number")] double baseNumber,
        [Description("Exponent")] double exponent)
    {
        return Math.Pow(baseNumber, exponent);
    }
}
