﻿using Microsoft.Extensions.Configuration;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using PlanningDemo.Planning;
using PlanningDemo.Memory;
using System.ComponentModel;

namespace PlanningDemo;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🧠 Advanced Planning & Memory Demo");
        Console.WriteLine("==================================\n");

        // Load configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();

        // Create kernel with plugins
        var kernel = CreateKernel(configuration);
        RegisterPlugins(kernel);

        // Run examples
        await RunCustomPlannerExample(kernel);
        await RunMemoryManagementExample(kernel);
        await RunContextHandlingExample(kernel);
        await RunWorkflowOrchestrationExample(kernel);

        Console.WriteLine("\n🎉 Advanced planning and memory examples completed!");
        Console.WriteLine("Next: Explore 05-examples for real-world applications.");
    }

    private static Kernel CreateKernel(IConfiguration configuration)
    {
        var builder = Kernel.CreateBuilder();

        var openAiApiKey = configuration["OpenAI:ApiKey"];
        var azureEndpoint = configuration["AzureOpenAI:Endpoint"];

        if (!string.IsNullOrEmpty(openAiApiKey) && openAiApiKey != "YOUR_OPENAI_API_KEY_HERE")
        {
            Console.WriteLine("🔗 Using OpenAI");
            builder.AddOpenAIChatCompletion(
                modelId: configuration["OpenAI:ModelId"] ?? "gpt-4o-mini",
                apiKey: openAiApiKey);
        }
        else if (!string.IsNullOrEmpty(azureEndpoint) && azureEndpoint != "YOUR_AZURE_OPENAI_ENDPOINT_HERE")
        {
            Console.WriteLine("🔗 Using Azure OpenAI");
            builder.AddAzureOpenAIChatCompletion(
                deploymentName: configuration["AzureOpenAI:DeploymentName"]!,
                endpoint: azureEndpoint,
                apiKey: configuration["AzureOpenAI:ApiKey"]!);
        }
        else
        {
            throw new InvalidOperationException(
                "Please configure either OpenAI or Azure OpenAI settings in appsettings.json");
        }

        return builder.Build();
    }

    private static void RegisterPlugins(Kernel kernel)
    {
        // Register utility plugins for planning examples
        kernel.ImportPluginFromType<FilePlugin>("File");
        kernel.ImportPluginFromType<WebPlugin>("Web");
        kernel.ImportPluginFromType<EmailPlugin>("Email");
        kernel.ImportPluginFromType<CalculatorPlugin>("Calculator");
    }

    private static async Task RunCustomPlannerExample(Kernel kernel)
    {
        Console.WriteLine("📋 Custom Planner Example");
        Console.WriteLine("-------------------------");

        var planner = new CustomPlanner(kernel);

        var goal = """
            I need to research the latest AI trends, create a summary report,
            calculate the market size, and email the report to my team.
            """;

        var plan = await planner.CreatePlanAsync(goal);
        Console.WriteLine($"Generated Plan:\n{plan}\n");

        var executionResult = await planner.ExecutePlanAsync(plan);
        Console.WriteLine($"Execution Result:\n{executionResult}\n");
    }

    private static async Task RunMemoryManagementExample(Kernel kernel)
    {
        Console.WriteLine("🧠 Memory Management Example");
        Console.WriteLine("----------------------------");

        var memoryManager = new ConversationMemory();

        // Simulate a conversation with memory
        var conversations = new[]
        {
            "Hi, I'm working on a machine learning project about image classification.",
            "I'm using TensorFlow and having trouble with overfitting.",
            "What techniques can I use to reduce overfitting?",
            "Can you remind me what my project was about?"
        };

        foreach (var message in conversations)
        {
            Console.WriteLine($"User: {message}");

            // Store the message in memory
            await memoryManager.StoreMessageAsync("user", message);

            // Get relevant context from memory
            var context = await memoryManager.GetRelevantContextAsync(message);

            // Generate response with context
            var response = await GenerateContextualResponseAsync(kernel, message, context);
            Console.WriteLine($"Assistant: {response}");

            // Store the response in memory
            await memoryManager.StoreMessageAsync("assistant", response);
            Console.WriteLine();
        }
    }

    private static async Task RunContextHandlingExample(Kernel kernel)
    {
        Console.WriteLine("🔄 Context Handling Example");
        Console.WriteLine("---------------------------");

        var contextManager = new ContextManager();

        // Set up initial context
        contextManager.SetContext("user_name", "Alice");
        contextManager.SetContext("project", "E-commerce Website");
        contextManager.SetContext("role", "Project Manager");

        var tasks = new[]
        {
            "Create a project timeline",
            "Identify potential risks",
            "Estimate budget requirements",
            "Plan team structure"
        };

        foreach (var task in tasks)
        {
            Console.WriteLine($"Task: {task}");

            var contextualPrompt = contextManager.BuildContextualPrompt(task);
            var result = await kernel.InvokePromptAsync(contextualPrompt);

            Console.WriteLine($"Result: {result}\n");

            // Update context based on results
            contextManager.UpdateContext("last_task", task);
            contextManager.UpdateContext("last_result", result.ToString());
        }
    }

    private static async Task RunWorkflowOrchestrationExample(Kernel kernel)
    {
        Console.WriteLine("🔀 Workflow Orchestration Example");
        Console.WriteLine("---------------------------------");

        var orchestrator = new WorkflowOrchestrator(kernel);

        // Define a complex workflow
        var workflow = new WorkflowDefinition
        {
            Name = "Content Creation Workflow",
            Steps = new[]
            {
                new WorkflowStep { Name = "Research Topic", Function = "ResearchTopic" },
                new WorkflowStep { Name = "Create Outline", Function = "CreateOutline" },
                new WorkflowStep { Name = "Write Content", Function = "WriteContent" },
                new WorkflowStep { Name = "Review Content", Function = "ReviewContent" },
                new WorkflowStep { Name = "Finalize", Function = "FinalizeContent" }
            }
        };

        var input = "Create a blog post about the benefits of AI in healthcare";
        var result = await orchestrator.ExecuteWorkflowAsync(workflow, input);

        Console.WriteLine($"Workflow Result:\n{result}");
    }

    private static async Task<string> GenerateContextualResponseAsync(Kernel kernel, string message, string context)
    {
        var prompt = $"""
            Context from previous conversation:
            {context}

            Current message: {message}

            Provide a helpful response that takes into account the conversation history.
            """;

        var result = await kernel.InvokePromptAsync(prompt);
        return result.ToString();
    }
}
