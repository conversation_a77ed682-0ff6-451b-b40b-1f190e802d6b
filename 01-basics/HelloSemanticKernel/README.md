# Hello Semantic Kernel

This is your first introduction to Microsoft Semantic Kernel! This example demonstrates the fundamental concepts of setting up and using Semantic Kernel for AI-powered applications.

## 🎯 What You'll Learn

- How to set up a Semantic Kernel instance
- Basic AI chat completion
- Using prompt templates with variables
- Creating and calling simple functions
- Configuration management for AI services

## 🚀 Getting Started

### Prerequisites

- .NET 8.0 SDK
- An OpenAI API key OR Azure OpenAI service

### Setup

1. **Configure your AI service**:
   - Open `appsettings.json`
   - Add your OpenAI API key OR Azure OpenAI configuration
   - The app will automatically detect which service to use

2. **Run the example**:
   ```bash
   dotnet run
   ```

### Configuration Options

#### Option 1: OpenAI
```json
{
  "OpenAI": {
    "ApiKey": "sk-your-api-key-here",
    "ModelId": "gpt-4o-mini"
  }
}
```

#### Option 2: Azure OpenAI
```json
{
  "AzureOpenAI": {
    "Endpoint": "https://your-resource.openai.azure.com/",
    "ApiKey": "your-azure-api-key",
    "DeploymentName": "your-deployment-name"
  }
}
```

## 📚 Key Concepts Demonstrated

### 1. Kernel Creation
The `Kernel` is the central orchestration engine in Semantic Kernel. It manages:
- AI service connections
- Function registration and execution
- Plugin management
- Execution context

### 2. Prompt Invocation
Simple AI interactions using `InvokePromptAsync()`:
```csharp
var response = await kernel.InvokePromptAsync("Your prompt here");
```

### 3. Prompt Templates
Using variables in prompts for dynamic content:
```csharp
var template = "Explain {{$concept}} in {{$language}}";
var result = await kernel.InvokePromptAsync(template, new() {
    ["concept"] = "dependency injection",
    ["language"] = "C#"
});
```

### 4. Function Creation
Creating reusable functions from C# methods:
```csharp
var function = kernel.CreateFunctionFromMethod(
    () => DateTime.Now.ToString(),
    "GetCurrentTime",
    "Gets the current time");
```

## 🔍 What's Happening Under the Hood

1. **Configuration Loading**: The app loads settings from `appsettings.json` and environment variables
2. **Service Detection**: Automatically chooses between OpenAI and Azure OpenAI based on configuration
3. **Kernel Building**: Creates a kernel instance with the appropriate AI service
4. **Function Registration**: Registers native C# functions that the AI can call
5. **Prompt Execution**: Sends prompts to the AI service and returns responses

## 🎓 Learning Exercises

Try modifying the code to:

1. **Add more variables** to the prompt template
2. **Create additional functions** (e.g., weather, calculator)
3. **Experiment with different prompts** and see how the AI responds
4. **Add error handling** for API failures
5. **Try different AI models** by changing the ModelId

## 🔗 Next Steps

- **02-functions/**: Learn about semantic functions, native functions, and plugins
- **03-orchestration/**: Explore agent patterns and multi-agent systems
- **04-advanced/**: Dive into planning, memory, and context management

## 🛠️ Troubleshooting

### Common Issues

1. **"Please configure either OpenAI or Azure OpenAI"**
   - Make sure you've updated `appsettings.json` with valid API keys
   - Check that the keys don't contain the placeholder text

2. **API Key errors**
   - Verify your API key is correct and has sufficient credits
   - For Azure OpenAI, ensure your endpoint and deployment name are correct

3. **Network issues**
   - Check your internet connection
   - Verify firewall settings allow HTTPS requests

### Environment Variables

You can also set configuration via environment variables:
```bash
export OpenAI__ApiKey="your-api-key"
export OpenAI__ModelId="gpt-4o-mini"
```

## 📖 Additional Resources

- [Semantic Kernel Documentation](https://learn.microsoft.com/en-us/semantic-kernel/)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Azure OpenAI Documentation](https://learn.microsoft.com/en-us/azure/ai-services/openai/)
