{"version": 2, "dgSpecHash": "9nOWp8u7TXE=", "success": true, "projectFilePath": "/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/HelloSemanticKernel.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/azure.ai.openai/2.2.0-beta.4/azure.ai.openai.2.2.0-beta.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.core/1.44.1/azure.core.1.44.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/6.0.0/microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.hashcode/1.1.1/microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.ai/9.5.0/microsoft.extensions.ai.9.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.ai.abstractions/9.5.0/microsoft.extensions.ai.abstractions.9.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.ai.openai/9.5.0-preview.1.25265.7/microsoft.extensions.ai.openai.9.5.0-preview.1.25265.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/8.0.0/microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.6/microsoft.extensions.configuration.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.6/microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.environmentvariables/9.0.6/microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/9.0.6/microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/9.0.6/microsoft.extensions.configuration.json.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.1/microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.2/microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/9.0.6/microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/9.0.6/microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/9.0.6/microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.3/microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.6/microsoft.extensions.primitives.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.vectordata.abstractions/9.6.0/microsoft.extensions.vectordata.abstractions.9.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.semantickernel/1.58.0/microsoft.semantickernel.1.58.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.semantickernel.abstractions/1.58.0/microsoft.semantickernel.abstractions.1.58.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.semantickernel.connectors.azureopenai/1.58.0/microsoft.semantickernel.connectors.azureopenai.1.58.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.semantickernel.connectors.openai/1.58.0/microsoft.semantickernel.connectors.openai.1.58.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.semantickernel.core/1.58.0/microsoft.semantickernel.core.1.58.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/openai/2.2.0-beta.4/openai.2.2.0-beta.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.clientmodel/1.4.0-beta.1/system.clientmodel.1.4.0-beta.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/8.0.1/system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/9.0.6/system.io.pipelines.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory.data/8.0.1/system.memory.data.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.numerics.tensors/9.0.0/system.numerics.tensors.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.numerics.vectors/4.5.0/system.numerics.vectors.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/9.0.6/system.text.encodings.web.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/9.0.6/system.text.json.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.channels/8.0.0/system.threading.channels.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg.sha512"], "logs": []}