/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.csproj.AssemblyReference.cache
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.AssemblyInfoInputs.cache
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.AssemblyInfo.cs
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.csproj.CoreCompileInputs.cache
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/HelloSemanticKernel
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/HelloSemanticKernel.deps.json
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/HelloSemanticKernel.runtimeconfig.json
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/HelloSemanticKernel.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/HelloSemanticKernel.pdb
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Azure.AI.OpenAI.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Azure.Core.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Bcl.AsyncInterfaces.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Bcl.HashCode.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.AI.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.AI.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.AI.OpenAI.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Caching.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Configuration.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Json.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.FileProviders.Physical.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.SemanticKernel.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.SemanticKernel.Abstractions.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/Microsoft.SemanticKernel.Core.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/OpenAI.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/System.ClientModel.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/System.IO.Pipelines.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/System.Memory.Data.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/System.Numerics.Tensors.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/System.Text.Json.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSem.7E71C88C.Up2Date
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/refint/HelloSemanticKernel.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.pdb
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/HelloSemanticKernel.genruntimeconfig.cache
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/obj/Debug/net8.0/ref/HelloSemanticKernel.dll
/Users/<USER>/MSK/01-basics/HelloSemanticKernel/HelloSemanticKernel/bin/Debug/net8.0/appsettings.json
