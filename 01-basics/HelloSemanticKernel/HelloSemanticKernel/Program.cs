using Microsoft.Extensions.Configuration;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.OpenAI;

namespace HelloSemanticKernel;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🤖 Welcome to Semantic Kernel Learning!");
        Console.WriteLine("=====================================\n");

        // Load configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();

        // Create kernel with OpenAI
        var kernel = CreateKernel(configuration);

        // Run examples
        await RunBasicExamples(kernel);

        Console.WriteLine("\n🎉 Basic Semantic Kernel examples completed!");
        Console.WriteLine("Next: Explore 02-functions for more advanced capabilities.");
    }

    private static Kernel CreateKernel(IConfiguration configuration)
    {
        var builder = Kernel.CreateBuilder();

        // Try to use OpenAI first, fall back to Azure OpenAI
        var openAiApiKey = configuration["OpenAI:ApiKey"];
        var azureEndpoint = configuration["AzureOpenAI:Endpoint"];

        if (!string.IsNullOrEmpty(openAiApiKey) && openAiApiKey != "YOUR_OPENAI_API_KEY_HERE")
        {
            Console.WriteLine("🔗 Using OpenAI");
            builder.AddOpenAIChatCompletion(
                modelId: configuration["OpenAI:ModelId"] ?? "gpt-4o-mini",
                apiKey: openAiApiKey);
        }
        else if (!string.IsNullOrEmpty(azureEndpoint) && azureEndpoint != "YOUR_AZURE_OPENAI_ENDPOINT_HERE")
        {
            Console.WriteLine("🔗 Using Azure OpenAI");
            builder.AddAzureOpenAIChatCompletion(
                deploymentName: configuration["AzureOpenAI:DeploymentName"]!,
                endpoint: azureEndpoint,
                apiKey: configuration["AzureOpenAI:ApiKey"]!);
        }
        else
        {
            throw new InvalidOperationException(
                "Please configure either OpenAI or Azure OpenAI settings in appsettings.json");
        }

        return builder.Build();
    }

    private static async Task RunBasicExamples(Kernel kernel)
    {
        Console.WriteLine("📝 Example 1: Simple AI Chat");
        Console.WriteLine("----------------------------");

        var response = await kernel.InvokePromptAsync("What is Semantic Kernel in one sentence?");
        Console.WriteLine($"AI: {response}");

        Console.WriteLine("\n📝 Example 2: Prompt with Variables");
        Console.WriteLine("-----------------------------------");

        var promptTemplate = """
            You are a helpful programming tutor.
            Explain {{$concept}} in {{$language}} programming language.
            Keep it concise and include a simple code example.
            """;

        var result = await kernel.InvokePromptAsync(promptTemplate, new()
        {
            ["concept"] = "dependency injection",
            ["language"] = "C#"
        });

        Console.WriteLine($"AI: {result}");

        Console.WriteLine("\n📝 Example 3: Function Calling");
        Console.WriteLine("------------------------------");

        // Create a simple function
        var getCurrentTime = kernel.CreateFunctionFromMethod(
            () => DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            "GetCurrentTime",
            "Gets the current date and time");

        var timePrompt = "What time is it? Use the GetCurrentTime function and format your response nicely.";
        var timeResult = await kernel.InvokePromptAsync(timePrompt);
        Console.WriteLine($"AI: {timeResult}");
    }
}
