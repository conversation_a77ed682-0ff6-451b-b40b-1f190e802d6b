using Microsoft.SemanticKernel;

namespace SingleAgentDemo.Agents;

/// <summary>
/// An agent specialized in project planning and task breakdown.
/// </summary>
public class TaskPlannerAgent
{
    private readonly Kernel _kernel;
    private readonly KernelFunction _projectPlanFunction;
    private readonly KernelFunction _taskBreakdownFunction;
    private readonly KernelFunction _timeEstimationFunction;

    public TaskPlannerAgent(Kernel kernel)
    {
        _kernel = kernel;
        _projectPlanFunction = CreateProjectPlanFunction();
        _taskBreakdownFunction = CreateTaskBreakdownFunction();
        _timeEstimationFunction = CreateTimeEstimationFunction();
    }

    public async Task<string> CreateProjectPlanAsync(string projectDescription)
    {
        var result = await _kernel.InvokeAsync(_projectPlanFunction, new()
        {
            ["projectDescription"] = projectDescription
        });

        return result.ToString();
    }

    public async Task<string> BreakDownTaskAsync(string taskDescription)
    {
        var result = await _kernel.InvokeAsync(_taskBreakdownFunction, new()
        {
            ["taskDescription"] = taskDescription
        });

        return result.ToString();
    }

    public async Task<string> EstimateTimeAsync(string taskDescription, string complexity = "medium")
    {
        var result = await _kernel.InvokeAsync(_timeEstimationFunction, new()
        {
            ["taskDescription"] = taskDescription,
            ["complexity"] = complexity
        });

        return result.ToString();
    }

    private KernelFunction CreateProjectPlanFunction()
    {
        var prompt = """
            You are an expert project manager. Create a comprehensive project plan based on the following description:

            {{$projectDescription}}

            Please provide:
            1. Project overview and objectives
            2. Major phases and milestones
            3. Key deliverables
            4. Resource requirements
            5. Timeline estimates
            6. Risk considerations
            7. Success criteria

            Format the response as a structured plan with clear sections and bullet points.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "CreateProjectPlan");
    }

    private KernelFunction CreateTaskBreakdownFunction()
    {
        var prompt = """
            You are an expert at breaking down complex tasks into manageable subtasks.

            Task to break down: {{$taskDescription}}

            Please provide:
            1. A list of specific subtasks needed to complete this task
            2. Dependencies between subtasks
            3. Estimated effort for each subtask (in hours or days)
            4. Required skills or expertise for each subtask
            5. Acceptance criteria for each subtask

            Format as a numbered list with clear descriptions and details for each subtask.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "BreakDownTask");
    }

    private KernelFunction CreateTimeEstimationFunction()
    {
        var prompt = """
            You are an expert at software development time estimation.

            Task: {{$taskDescription}}
            Complexity Level: {{$complexity}} (low/medium/high)

            Please provide:
            1. Detailed time estimate with breakdown
            2. Factors that could affect the timeline
            3. Best case, most likely, and worst case scenarios
            4. Recommendations for reducing time if needed

            Consider factors like:
            - Technical complexity
            - Team experience
            - Dependencies
            - Testing requirements
            - Documentation needs
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "EstimateTime");
    }
}
