using Microsoft.SemanticKernel;

namespace SingleAgentDemo.Agents;

/// <summary>
/// An agent specialized in code analysis, review, and improvement suggestions.
/// </summary>
public class CodeAnalysisAgent
{
    private readonly Kernel _kernel;
    private readonly KernelFunction _codeAnalysisFunction;
    private readonly KernelFunction _improvementFunction;
    private readonly KernelFunction _securityAnalysisFunction;
    private readonly KernelFunction _performanceAnalysisFunction;

    public CodeAnalysisAgent(Kernel kernel)
    {
        _kernel = kernel;
        _codeAnalysisFunction = CreateCodeAnalysisFunction();
        _improvementFunction = CreateImprovementFunction();
        _securityAnalysisFunction = CreateSecurityAnalysisFunction();
        _performanceAnalysisFunction = CreatePerformanceAnalysisFunction();
    }

    public async Task<string> AnalyzeCodeAsync(string code, string language)
    {
        var result = await _kernel.InvokeAsync(_codeAnalysisFunction, new()
        {
            ["code"] = code,
            ["language"] = language
        });

        return result.ToString();
    }

    public async Task<string> SuggestImprovementsAsync(string code, string language)
    {
        var result = await _kernel.InvokeAsync(_improvementFunction, new()
        {
            ["code"] = code,
            ["language"] = language
        });

        return result.ToString();
    }

    public async Task<string> AnalyzeSecurityAsync(string code, string language)
    {
        var result = await _kernel.InvokeAsync(_securityAnalysisFunction, new()
        {
            ["code"] = code,
            ["language"] = language
        });

        return result.ToString();
    }

    public async Task<string> AnalyzePerformanceAsync(string code, string language)
    {
        var result = await _kernel.InvokeAsync(_performanceAnalysisFunction, new()
        {
            ["code"] = code,
            ["language"] = language
        });

        return result.ToString();
    }

    private KernelFunction CreateCodeAnalysisFunction()
    {
        var prompt = """
            You are an expert code reviewer. Analyze the following {{$language}} code:

            ```{{$language}}
            {{$code}}
            ```

            Please provide:
            1. Code quality assessment
            2. Adherence to best practices
            3. Potential bugs or issues
            4. Code structure and organization
            5. Readability and maintainability
            6. Error handling evaluation
            7. Documentation quality
            8. Overall rating (1-10) with justification

            Be specific and provide examples where possible.
            Focus on both positive aspects and areas for improvement.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "AnalyzeCode");
    }

    private KernelFunction CreateImprovementFunction()
    {
        var prompt = """
            You are an expert software developer. Review the following {{$language}} code and suggest improvements:

            ```{{$language}}
            {{$code}}
            ```

            Please provide:
            1. Specific improvement suggestions with explanations
            2. Refactored code examples where applicable
            3. Design pattern recommendations
            4. Performance optimizations
            5. Code organization improvements
            6. Error handling enhancements
            7. Testing recommendations
            8. Documentation improvements

            Prioritize suggestions by impact and provide clear rationale for each recommendation.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "SuggestImprovements");
    }

    private KernelFunction CreateSecurityAnalysisFunction()
    {
        var prompt = """
            You are a cybersecurity expert specializing in code security. Analyze the following {{$language}} code for security vulnerabilities:

            ```{{$language}}
            {{$code}}
            ```

            Please provide:
            1. Identified security vulnerabilities
            2. Risk level for each vulnerability (Critical/High/Medium/Low)
            3. Potential attack vectors
            4. Mitigation strategies
            5. Secure coding recommendations
            6. Input validation assessment
            7. Authentication and authorization concerns
            8. Data protection considerations

            Be thorough and provide specific examples of how vulnerabilities could be exploited.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "AnalyzeSecurity");
    }

    private KernelFunction CreatePerformanceAnalysisFunction()
    {
        var prompt = """
            You are a performance optimization expert. Analyze the following {{$language}} code for performance issues:

            ```{{$language}}
            {{$code}}
            ```

            Please provide:
            1. Performance bottlenecks identification
            2. Time complexity analysis
            3. Space complexity analysis
            4. Memory usage concerns
            5. Optimization opportunities
            6. Algorithmic improvements
            7. Data structure recommendations
            8. Scalability considerations

            Provide specific suggestions for improving performance and explain the expected impact.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "AnalyzePerformance");
    }
}
