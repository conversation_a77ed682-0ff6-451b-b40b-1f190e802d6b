using Microsoft.SemanticKernel;

namespace SingleAgentDemo.Agents;

/// <summary>
/// An agent specialized in customer service interactions and issue resolution.
/// </summary>
public class CustomerServiceAgent
{
    private readonly Kernel _kernel;
    private readonly KernelFunction _inquiryHandlerFunction;
    private readonly KernelFunction _escalationFunction;
    private readonly KernelFunction _sentimentAnalysisFunction;
    private readonly KernelFunction _solutionGeneratorFunction;

    public CustomerServiceAgent(Kernel kernel)
    {
        _kernel = kernel;
        _inquiryHandlerFunction = CreateInquiryHandlerFunction();
        _escalationFunction = CreateEscalationFunction();
        _sentimentAnalysisFunction = CreateSentimentAnalysisFunction();
        _solutionGeneratorFunction = CreateSolutionGeneratorFunction();
    }

    public async Task<string> HandleInquiryAsync(string customerInquiry)
    {
        var result = await _kernel.InvokeAsync(_inquiryHandlerFunction, new()
        {
            ["inquiry"] = customerInquiry
        });

        return result.ToString();
    }

    public async Task<string> EscalateIssueAsync(string issue)
    {
        var result = await _kernel.InvokeAsync(_escalationFunction, new()
        {
            ["issue"] = issue
        });

        return result.ToString();
    }

    public async Task<string> AnalyzeSentimentAsync(string customerMessage)
    {
        var result = await _kernel.InvokeAsync(_sentimentAnalysisFunction, new()
        {
            ["message"] = customerMessage
        });

        return result.ToString();
    }

    public async Task<string> GenerateSolutionAsync(string problem)
    {
        var result = await _kernel.InvokeAsync(_solutionGeneratorFunction, new()
        {
            ["problem"] = problem
        });

        return result.ToString();
    }

    private KernelFunction CreateInquiryHandlerFunction()
    {
        var prompt = """
            You are a professional customer service representative. Handle the following customer inquiry with empathy and efficiency:

            Customer Inquiry: {{$inquiry}}

            Please provide:
            1. A warm, professional greeting acknowledging their concern
            2. Clear understanding of their issue
            3. Step-by-step solution or guidance
            4. Alternative options if applicable
            5. Follow-up instructions
            6. Offer for additional assistance

            Guidelines:
            - Be empathetic and understanding
            - Use clear, simple language
            - Provide specific, actionable steps
            - Maintain a helpful and positive tone
            - If you cannot resolve the issue, explain next steps clearly
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "HandleInquiry");
    }

    private KernelFunction CreateEscalationFunction()
    {
        var prompt = """
            You are a senior customer service manager handling an escalated issue:

            Escalated Issue: {{$issue}}

            Please provide:
            1. Acknowledgment of the issue's complexity and customer's frustration
            2. Detailed analysis of the problem
            3. Comprehensive resolution plan with timeline
            4. Compensation or goodwill gestures if appropriate
            5. Prevention measures for similar issues
            6. Direct contact information for follow-up
            7. Escalation to higher management if needed

            This is a high-priority issue requiring immediate attention and a thorough response.
            Show genuine concern and commitment to resolution.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "EscalateIssue");
    }

    private KernelFunction CreateSentimentAnalysisFunction()
    {
        var prompt = """
            You are an expert in customer sentiment analysis. Analyze the following customer message:

            Customer Message: {{$message}}

            Please provide:
            1. Overall sentiment (Positive/Neutral/Negative)
            2. Emotional indicators (frustrated, angry, confused, satisfied, etc.)
            3. Urgency level (Low/Medium/High/Critical)
            4. Key concerns or pain points
            5. Customer's likely expectations
            6. Recommended response approach
            7. Risk of customer churn

            Be specific about emotional cues and provide actionable insights for the response strategy.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "AnalyzeSentiment");
    }

    private KernelFunction CreateSolutionGeneratorFunction()
    {
        var prompt = """
            You are a problem-solving expert in customer service. Generate solutions for the following problem:

            Problem: {{$problem}}

            Please provide:
            1. Multiple solution options (at least 3)
            2. Pros and cons of each solution
            3. Implementation steps for each option
            4. Resource requirements
            5. Timeline estimates
            6. Risk assessment
            7. Recommended solution with justification

            Consider both immediate fixes and long-term solutions.
            Think creatively and consider the customer's perspective.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "GenerateSolution");
    }
}
