using Microsoft.SemanticKernel;

namespace SingleAgentDemo.Agents;

/// <summary>
/// An agent specialized in research, analysis, and information synthesis.
/// </summary>
public class ResearchAgent
{
    private readonly Kernel _kernel;
    private readonly KernelFunction _researchFunction;
    private readonly KernelFunction _analysisFunction;
    private readonly KernelFunction _synthesisFunction;
    private readonly KernelFunction _factCheckFunction;

    public ResearchAgent(Kernel kernel)
    {
        _kernel = kernel;
        _researchFunction = CreateResearchFunction();
        _analysisFunction = CreateAnalysisFunction();
        _synthesisFunction = CreateSynthesisFunction();
        _factCheckFunction = CreateFactCheckFunction();
    }

    public async Task<string> ConductResearchAsync(string topic)
    {
        var result = await _kernel.InvokeAsync(_researchFunction, new()
        {
            ["topic"] = topic
        });

        return result.ToString();
    }

    public async Task<string> AnalyzeInformationAsync(string information)
    {
        var result = await _kernel.InvokeAsync(_analysisFunction, new()
        {
            ["information"] = information
        });

        return result.ToString();
    }

    public async Task<string> SynthesizeInformationAsync(string[] sources)
    {
        var combinedSources = string.Join("\n\n---\n\n", sources);
        var result = await _kernel.InvokeAsync(_synthesisFunction, new()
        {
            ["sources"] = combinedSources
        });

        return result.ToString();
    }

    public async Task<string> FactCheckAsync(string claim)
    {
        var result = await _kernel.InvokeAsync(_factCheckFunction, new()
        {
            ["claim"] = claim
        });

        return result.ToString();
    }

    private KernelFunction CreateResearchFunction()
    {
        var prompt = """
            You are an expert researcher. Conduct comprehensive research on the following topic:

            Topic: {{$topic}}

            Please provide:
            1. Overview of the topic
            2. Key concepts and definitions
            3. Current state of the field
            4. Recent developments and trends
            5. Major players or organizations involved
            6. Challenges and opportunities
            7. Future outlook
            8. Relevant statistics or data points

            Structure your response with clear headings and provide detailed, accurate information.
            Focus on factual, well-established information and clearly indicate when something is speculative.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "ConductResearch");
    }

    private KernelFunction CreateAnalysisFunction()
    {
        var prompt = """
            You are an expert analyst. Analyze the following information thoroughly:

            Information to analyze: {{$information}}

            Please provide:
            1. Key insights and patterns
            2. Strengths and weaknesses
            3. Opportunities and threats
            4. Cause and effect relationships
            5. Implications and consequences
            6. Recommendations based on the analysis
            7. Areas requiring further investigation

            Use critical thinking and provide evidence-based analysis.
            Consider multiple perspectives and potential biases.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "AnalyzeInformation");
    }

    private KernelFunction CreateSynthesisFunction()
    {
        var prompt = """
            You are an expert at synthesizing information from multiple sources.

            Sources to synthesize:
            {{$sources}}

            Please provide:
            1. Common themes and patterns across sources
            2. Conflicting information and potential reasons
            3. Gaps in the information
            4. Integrated summary that combines key insights
            5. Reliability assessment of different sources
            6. Conclusions that can be drawn from the combined information

            Create a coherent narrative that brings together the best insights from all sources.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "SynthesizeInformation");
    }

    private KernelFunction CreateFactCheckFunction()
    {
        var prompt = """
            You are an expert fact-checker. Evaluate the following claim:

            Claim: {{$claim}}

            Please provide:
            1. Assessment of the claim's accuracy (True/False/Partially True/Unverifiable)
            2. Evidence supporting or contradicting the claim
            3. Context that might affect the claim's validity
            4. Potential sources of bias or misinformation
            5. Recommendations for verification
            6. Related facts that provide additional context

            Be thorough and objective in your assessment.
            Clearly distinguish between facts, opinions, and speculation.
            """;

        return _kernel.CreateFunctionFromPrompt(prompt, "FactCheck");
    }
}
