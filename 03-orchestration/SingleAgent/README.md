# Single Agent Orchestration

This module demonstrates how to create specialized AI agents using Semantic Kernel. Each agent is designed for a specific domain and showcases different patterns of AI orchestration within a single agent architecture.

## 🎯 What You'll Learn

- Creating specialized AI agents with domain expertise
- Agent architecture patterns and design principles
- Function composition within agents
- Context management and state handling
- Agent interaction patterns
- Real-world agent implementation examples

## 🚀 Getting Started

### Prerequisites

- .NET 8.0 SDK
- OpenAI API key or Azure OpenAI service
- Completed the `02-functions` module

### Setup

1. **Configure your AI service** in `appsettings.json`
2. **Run the demo**:
   ```bash
   cd SingleAgentDemo
   dotnet run
   ```

## 🤖 Agent Examples

### 1. Task Planner Agent
**Purpose**: Project planning and task breakdown
**Capabilities**:
- Create comprehensive project plans
- Break down complex tasks into subtasks
- Estimate time and effort requirements
- Identify dependencies and risks

**Key Functions**:
- `CreateProjectPlanAsync()` - Generate detailed project plans
- `BreakDownTaskAsync()` - Decompose tasks into manageable pieces
- `EstimateTimeAsync()` - Provide realistic time estimates

### 2. Research Agent
**Purpose**: Information gathering, analysis, and synthesis
**Capabilities**:
- Conduct comprehensive research on topics
- Analyze information from multiple perspectives
- Synthesize findings from various sources
- Fact-check claims and statements

**Key Functions**:
- `ConductResearchAsync()` - Perform thorough research
- `AnalyzeInformationAsync()` - Provide detailed analysis
- `SynthesizeInformationAsync()` - Combine multiple sources
- `FactCheckAsync()` - Verify claims and statements

### 3. Code Analysis Agent
**Purpose**: Code review, analysis, and improvement
**Capabilities**:
- Analyze code quality and structure
- Identify bugs and potential issues
- Suggest improvements and optimizations
- Perform security and performance analysis

**Key Functions**:
- `AnalyzeCodeAsync()` - Comprehensive code review
- `SuggestImprovementsAsync()` - Provide enhancement recommendations
- `AnalyzeSecurityAsync()` - Identify security vulnerabilities
- `AnalyzePerformanceAsync()` - Find performance bottlenecks

### 4. Customer Service Agent
**Purpose**: Customer support and issue resolution
**Capabilities**:
- Handle customer inquiries professionally
- Analyze customer sentiment and emotions
- Generate solutions for problems
- Escalate complex issues appropriately

**Key Functions**:
- `HandleInquiryAsync()` - Process customer requests
- `AnalyzeSentimentAsync()` - Understand customer emotions
- `GenerateSolutionAsync()` - Create problem solutions
- `EscalateIssueAsync()` - Handle complex escalations

## 🏗️ Agent Architecture Patterns

### 1. Specialized Function Composition
Each agent combines multiple specialized functions:

```csharp
public class TaskPlannerAgent
{
    private readonly KernelFunction _projectPlanFunction;
    private readonly KernelFunction _taskBreakdownFunction;
    private readonly KernelFunction _timeEstimationFunction;
    
    // Functions work together to provide comprehensive planning
}
```

### 2. Domain-Specific Prompts
Agents use carefully crafted prompts for their domain:

```csharp
private KernelFunction CreateProjectPlanFunction()
{
    var prompt = """
        You are an expert project manager. Create a comprehensive project plan...
        
        Please provide:
        1. Project overview and objectives
        2. Major phases and milestones
        3. Key deliverables
        ...
        """;
    
    return _kernel.CreateFunctionFromPrompt(prompt, "CreateProjectPlan");
}
```

### 3. Context-Aware Processing
Agents maintain context and adapt responses based on input:

```csharp
public async Task<string> HandleInquiryAsync(string customerInquiry)
{
    // Analyze sentiment first
    var sentiment = await AnalyzeSentimentAsync(customerInquiry);
    
    // Adapt response based on sentiment
    var response = await _kernel.InvokeAsync(_inquiryHandlerFunction, new()
    {
        ["inquiry"] = customerInquiry,
        ["sentiment"] = sentiment
    });
    
    return response.ToString();
}
```

## 🎨 Design Principles

### 1. Single Responsibility
Each agent focuses on one domain or type of task:
- **TaskPlannerAgent**: Only handles planning and task management
- **ResearchAgent**: Only handles research and analysis
- **CodeAnalysisAgent**: Only handles code-related tasks

### 2. Composable Functions
Agents combine multiple functions to provide comprehensive capabilities:
- Functions can be used independently
- Functions can be chained for complex workflows
- Functions share common context and state

### 3. Extensible Architecture
Easy to add new capabilities:
- Add new functions to existing agents
- Create new specialized agents
- Combine agents for multi-agent scenarios

## 🔍 Advanced Patterns

### Function Chaining
```csharp
// Research -> Analyze -> Synthesize workflow
var research = await researchAgent.ConductResearchAsync(topic);
var analysis = await researchAgent.AnalyzeInformationAsync(research);
var synthesis = await researchAgent.SynthesizeInformationAsync(new[] { research, analysis });
```

### Conditional Logic
```csharp
// Different handling based on complexity
if (complexity == "high")
{
    return await EscalateIssueAsync(issue);
}
else
{
    return await HandleInquiryAsync(issue);
}
```

### Context Preservation
```csharp
// Maintain conversation context
private readonly Dictionary<string, object> _context = new();

public async Task<string> ContinueConversationAsync(string input)
{
    _context["previousInput"] = input;
    _context["timestamp"] = DateTime.Now;
    
    // Use context in function calls
    var result = await _kernel.InvokeAsync(function, _context);
    return result.ToString();
}
```

## 🎓 Learning Exercises

1. **Create a new agent** for a different domain (e.g., FinancialAdvisorAgent)
2. **Add state management** to track conversation history
3. **Implement agent memory** to remember previous interactions
4. **Create agent workflows** that chain multiple functions
5. **Add error handling** and retry logic to agents
6. **Implement agent metrics** to track performance and usage

## 🔗 Agent Communication Patterns

### Internal Function Calls
```csharp
// Agent calls its own functions
var analysis = await AnalyzeCodeAsync(code, language);
var improvements = await SuggestImprovementsAsync(code, language);
```

### Cross-Agent Communication (Preview for Multi-Agent)
```csharp
// Agents can share information
var researchResults = await researchAgent.ConductResearchAsync(topic);
var codeAnalysis = await codeAgent.AnalyzeCodeAsync(researchResults);
```

## 🛠️ Best Practices

1. **Clear Agent Boundaries**: Each agent should have a well-defined purpose
2. **Consistent Interface**: Use similar patterns across all agents
3. **Error Handling**: Implement robust error handling and fallbacks
4. **Performance**: Cache results and optimize function calls
5. **Testing**: Test each agent function independently
6. **Documentation**: Provide clear descriptions for all functions
7. **Monitoring**: Track agent performance and usage metrics

## 🔗 Next Steps

- **MultiAgent/**: Learn about multi-agent coordination and communication
- **AgentCommunication/**: Explore inter-agent messaging patterns
- **04-advanced/**: Dive into planning, memory, and context management

## 📖 Additional Resources

- [AI Agent Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/ai-ml/guide/agent-design-patterns)
- [Semantic Kernel Agent Framework](https://learn.microsoft.com/en-us/semantic-kernel/agents/)
- [Building Intelligent Agents](https://learn.microsoft.com/en-us/azure/ai-services/agents/)
