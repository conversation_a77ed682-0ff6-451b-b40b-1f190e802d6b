# Semantic Kernel & AI Agent Orchestration Learning Project

Welcome to your comprehensive learning project for Microsoft Semantic Kernel and AI agent orchestration! This project is designed to take you from basic concepts to advanced agent orchestration patterns.

## 🎯 Learning Objectives

By the end of this project, you'll understand:
- Core Semantic Kernel concepts and architecture
- How to create and manage AI functions and plugins
- Agent orchestration patterns and multi-agent systems
- Planning, memory, and context management
- Real-world application patterns

## 📁 Project Structure

```
├── 01-basics/                 # Basic Semantic Kernel concepts
│   └── HelloSemanticKernel/   # Complete introduction with kernel setup and basic AI interactions
├── 02-functions/              # Functions and plugins
│   └── SemanticFunctions/     # Comprehensive demo: semantic functions, native functions, and plugins
├── 03-orchestration/          # Agent orchestration
│   └── SingleAgent/           # Specialized AI agents with domain expertise
├── 04-advanced/               # Advanced features
│   └── Planning/              # Planning, memory management, and workflow orchestration
└── docs/                      # Documentation and guides
    ├── GETTING_STARTED.md     # Setup and first steps
    └── LEARNING_GUIDE.md      # Complete learning path
```

## 🚀 Getting Started

1. **Prerequisites**: .NET 8.0 SDK, Visual Studio Code or Visual Studio
2. **API Keys**: You'll need an OpenAI API key or Azure OpenAI endpoint
3. **Start with**: `01-basics/HelloSemanticKernel/`

## 📚 Learning Path

1. **01-basics/HelloSemanticKernel/**: Kernel setup, configuration, and basic AI interactions
2. **02-functions/SemanticFunctions/**: Semantic functions, native functions, and plugin development
3. **03-orchestration/SingleAgent/**: Specialized AI agents with domain expertise
4. **04-advanced/Planning/**: Planning, memory management, and workflow orchestration

## 🔧 Setup Instructions

Each module contains its own README with specific setup instructions. Generally:

1. Navigate to the module directory
2. Run `dotnet restore` to install dependencies
3. Configure your API keys in `appsettings.json` or environment variables
4. Run `dotnet run` to execute the examples

## 🎓 Key Concepts You'll Learn

- **Kernel**: The core orchestration engine
- **Functions**: AI-powered and native code functions
- **Plugins**: Collections of related functions
- **Planners**: Automatic task decomposition and execution
- **Memory**: Persistent and semantic memory systems
- **Agents**: Autonomous AI entities with specific roles

## 📖 Additional Resources

- [Microsoft Semantic Kernel Documentation](https://learn.microsoft.com/en-us/semantic-kernel/)
- [Semantic Kernel GitHub Repository](https://github.com/microsoft/semantic-kernel)
- [AI Agent Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/ai-ml/guide/agent-design-patterns)

## 🎓 Learning Path

### Phase 1: Foundations (Weeks 1-2)
- **01-basics/HelloSemanticKernel/**: Core concepts, kernel setup, and basic AI interactions
- **02-functions/SemanticFunctions/**: Function creation and plugin development

### Phase 2: Agent Development (Weeks 3-4)
- **03-orchestration/SingleAgent/**: Specialized AI agents with domain expertise

### Phase 3: Advanced Features (Weeks 5-6)
- **04-advanced/Planning/**: Planning, memory management, and workflow orchestration

## 🚀 Quick Start

1. **Prerequisites**: .NET 8.0 SDK, OpenAI API key or Azure OpenAI
2. **Setup**: Clone repo, run `dotnet restore && dotnet build`
3. **Configure**: Add your API key to `01-basics/HelloSemanticKernel/HelloSemanticKernel/appsettings.json`
4. **Run**: `cd 01-basics/HelloSemanticKernel/HelloSemanticKernel && dotnet run`

See [Getting Started Guide](docs/GETTING_STARTED.md) for detailed setup instructions.

## 📊 Project Status

✅ **Completed Modules**:
- [x] Basic Semantic Kernel introduction and setup
- [x] Semantic functions, native functions, and plugins
- [x] Single agent orchestration patterns
- [x] Advanced planning and memory management
- [x] Comprehensive documentation and learning guides

🎯 **Learning Outcomes**:
- Understand Semantic Kernel architecture and concepts
- Build reusable AI functions and plugins
- Create specialized AI agents for different domains
- Implement advanced planning and memory systems
- Apply knowledge to real-world scenarios

## 🛠️ Technical Features

- **Multiple AI Service Support**: OpenAI and Azure OpenAI
- **Comprehensive Examples**: From basic to advanced patterns
- **Production-Ready Code**: Error handling, logging, and best practices
- **Extensible Architecture**: Easy to add new agents and capabilities
- **Rich Documentation**: Step-by-step guides and explanations

## 🤝 Contributing

This is a learning project, but contributions are welcome:
- Report issues or bugs
- Suggest improvements to examples
- Add new learning exercises
- Improve documentation

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

Happy learning! 🤖✨
