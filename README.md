# Semantic Kernel & AI Agent Orchestration Learning Project

Welcome to your comprehensive learning project for Microsoft Semantic Kernel and AI agent orchestration! This project is designed to take you from basic concepts to advanced agent orchestration patterns.

## 🎯 Learning Objectives

By the end of this project, you'll understand:
- Core Semantic Kernel concepts and architecture
- How to create and manage AI functions and plugins
- Agent orchestration patterns and multi-agent systems
- Planning, memory, and context management
- Real-world application patterns

## 📁 Project Structure

```
├── 01-basics/                 # Basic Semantic Kernel concepts
│   ├── HelloSemanticKernel/   # Simple console app introduction
│   └── KernelConfiguration/   # Kernel setup and configuration
├── 02-functions/              # Functions and plugins
│   ├── SemanticFunctions/     # Prompt-based functions
│   ├── NativeFunctions/       # C# code functions
│   └── Plugins/               # Plugin development
├── 03-orchestration/          # Agent orchestration
│   ├── SingleAgent/           # Single agent patterns
│   ├── MultiAgent/            # Multi-agent coordination
│   └── AgentCommunication/    # Inter-agent communication
├── 04-advanced/               # Advanced features
│   ├── Planning/              # Automatic planning
│   ├── Memory/                # Memory management
│   └── ContextHandling/       # Context and state management
├── 05-examples/               # Real-world examples
│   ├── ChatBot/               # Conversational agent
│   ├── TaskAutomation/        # Task automation agent
│   └── ResearchAssistant/     # Research and analysis agent
└── docs/                      # Documentation and guides
```

## 🚀 Getting Started

1. **Prerequisites**: .NET 8.0 SDK, Visual Studio Code or Visual Studio
2. **API Keys**: You'll need an OpenAI API key or Azure OpenAI endpoint
3. **Start with**: `01-basics/HelloSemanticKernel/`

## 📚 Learning Path

1. **Start Here**: Basic Semantic Kernel setup and simple AI calls
2. **Functions**: Learn to create reusable AI functions and plugins
3. **Orchestration**: Build multi-agent systems and coordination patterns
4. **Advanced**: Explore planning, memory, and context management
5. **Examples**: Apply knowledge to real-world scenarios

## 🔧 Setup Instructions

Each module contains its own README with specific setup instructions. Generally:

1. Navigate to the module directory
2. Run `dotnet restore` to install dependencies
3. Configure your API keys in `appsettings.json` or environment variables
4. Run `dotnet run` to execute the examples

## 🎓 Key Concepts You'll Learn

- **Kernel**: The core orchestration engine
- **Functions**: AI-powered and native code functions
- **Plugins**: Collections of related functions
- **Planners**: Automatic task decomposition and execution
- **Memory**: Persistent and semantic memory systems
- **Agents**: Autonomous AI entities with specific roles

## 📖 Additional Resources

- [Microsoft Semantic Kernel Documentation](https://learn.microsoft.com/en-us/semantic-kernel/)
- [Semantic Kernel GitHub Repository](https://github.com/microsoft/semantic-kernel)
- [AI Agent Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/ai-ml/guide/agent-design-patterns)

Happy learning! 🤖✨
