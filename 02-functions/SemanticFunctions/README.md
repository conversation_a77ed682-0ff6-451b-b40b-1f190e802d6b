# Semantic Functions & Plugins

This module demonstrates the power of Seman<PERSON> Kernel's function system, including semantic functions (AI-powered), native functions (C# code), and plugins (collections of related functions).

## 🎯 What You'll Learn

- Creating semantic functions with prompt templates
- Building native functions from C# methods
- Organizing functions into reusable plugins
- Function composition and chaining
- Parameter handling and type conversion
- Advanced function orchestration patterns

## 🚀 Getting Started

### Prerequisites

- .NET 8.0 SDK
- OpenAI API key or Azure OpenAI service
- Completed the `01-basics` module

### Setup

1. **Configure your AI service** in `appsettings.json`
2. **Run the demo**:
   ```bash
   cd SemanticFunctionsDemo
   dotnet run
   ```

## 📚 Key Concepts

### 1. Semantic Functions
AI-powered functions created from prompt templates:

```csharp
var summarizeFunction = kernel.CreateFunctionFromPrompt(
    "Summarize the following text in 2-3 sentences: {{$input}}",
    functionName: "Summarize",
    description: "Summarizes text content");
```

**Key Features:**
- Use natural language prompts
- Support parameter substitution with `{{$paramName}}`
- Leverage AI model capabilities
- Can be composed with other functions

### 2. Native Functions
C# methods exposed as kernel functions:

```csharp
var getCurrentWeather = kernel.CreateFunctionFromMethod(
    (string location) => $"The weather in {location} is sunny with 72°F",
    "GetWeather",
    "Gets the current weather for a location");
```

**Key Features:**
- Execute deterministic C# code
- Type-safe parameter handling
- Can perform complex calculations
- Access external APIs and services

### 3. Plugins
Collections of related functions organized in classes:

```csharp
public class MathPlugin
{
    [KernelFunction]
    [Description("Adds two numbers together")]
    public int Add(
        [Description("The first number")] int a,
        [Description("The second number")] int b)
    {
        return a + b;
    }
}

// Register the plugin
kernel.ImportPluginFromType<MathPlugin>("Math");
```

**Key Features:**
- Organize related functionality
- Automatic function discovery
- Rich metadata with descriptions
- Reusable across different kernels

## 🔧 Plugin Examples

### MathPlugin
Mathematical operations and utilities:
- `Add`, `Subtract`, `Multiply`, `Divide`
- `Factorial`, `Power`, `SquareRoot`
- `IsPrime`, `RandomNumber`

### TextPlugin
Text manipulation and analysis:
- `ToUpper`, `ToLower`, `ToTitleCase`
- `WordCount`, `CharacterCount`, `Reverse`
- `Contains`, `Replace`, `Substring`
- `Split`, `Join`, `TrimWhitespace`

### TimePlugin
Date and time operations:
- `GetCurrentTime`, `GetCurrentDate`, `AddDays`
- `DaysBetween`, `FormatDate`, `IsLeapYear`
- `GetUnixTimestamp`, `UnixToDate`
- `GetStartOfWeek`, `GetEndOfWeek`

## 🎨 Function Composition

Functions can be chained and composed for complex workflows:

```csharp
// Chain multiple functions
var text = "SEMANTIC KERNEL IS AMAZING";
var lowerText = await kernel.InvokeAsync("Text", "ToLower", new() { ["text"] = text });
var wordCount = await kernel.InvokeAsync("Text", "WordCount", new() { ["text"] = lowerText.ToString() });

// Combine semantic and native functions
var calculation = await kernel.InvokeAsync("Math", "Factorial", new() { ["n"] = "6" });
var analysis = await kernel.InvokeAsync(analysisFunction, new()
{
    ["mathResult"] = calculation.ToString(),
    ["operation"] = "factorial of 6"
});
```

## 🔍 Advanced Features

### Parameter Descriptions
Use `[Description]` attributes to provide rich metadata:

```csharp
[KernelFunction]
[Description("Calculates the factorial of a number")]
public long Factorial([Description("The number to calculate factorial for")] int n)
```

### Error Handling
Implement proper validation and error handling:

```csharp
public double Divide(double a, double b)
{
    if (b == 0)
        throw new ArgumentException("Cannot divide by zero");
    return a / b;
}
```

### Type Conversion
Semantic Kernel automatically handles type conversion between string parameters and method parameters.

## 🎓 Learning Exercises

1. **Create a new plugin** for file operations (read, write, exists)
2. **Build a semantic function** that generates code from natural language
3. **Implement function chaining** to create a text processing pipeline
4. **Add error handling** to existing plugins
5. **Create a plugin** that integrates with external APIs

## 🔗 Function Invocation Patterns

### Direct Invocation
```csharp
var result = await kernel.InvokeAsync(functionName, parameters);
```

### Plugin Function Invocation
```csharp
var result = await kernel.InvokeAsync("PluginName", "FunctionName", parameters);
```

### Function Object Invocation
```csharp
var function = kernel.Plugins["PluginName"]["FunctionName"];
var result = await kernel.InvokeAsync(function, parameters);
```

## 🛠️ Best Practices

1. **Use descriptive names** for functions and parameters
2. **Provide clear descriptions** for all functions and parameters
3. **Implement proper validation** and error handling
4. **Group related functions** into logical plugins
5. **Keep functions focused** on a single responsibility
6. **Use appropriate return types** for your use case
7. **Test functions independently** before composing them

## 🔗 Next Steps

- **03-orchestration/**: Learn about agent patterns and multi-agent systems
- **04-advanced/**: Explore planning, memory, and context management
- **05-examples/**: See real-world applications of these concepts

## 📖 Additional Resources

- [Semantic Kernel Functions Documentation](https://learn.microsoft.com/en-us/semantic-kernel/ai-orchestration/plugins/)
- [Creating Plugins Guide](https://learn.microsoft.com/en-us/semantic-kernel/ai-orchestration/plugins/creating-plugins)
- [Function Calling Best Practices](https://learn.microsoft.com/en-us/semantic-kernel/ai-orchestration/plugins/best-practices)
