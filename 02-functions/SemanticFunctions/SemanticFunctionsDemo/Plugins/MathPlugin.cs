using System.ComponentModel;
using Microsoft.SemanticKernel;

namespace SemanticFunctionsDemo;

/// <summary>
/// A plugin that provides mathematical operations and utilities.
/// </summary>
public class MathPlugin
{
    [KernelFunction]
    [Description("Adds two numbers together")]
    public int Add(
        [Description("The first number")] int a,
        [Description("The second number")] int b)
    {
        return a + b;
    }

    [KernelFunction]
    [Description("Subtracts the second number from the first")]
    public int Subtract(
        [Description("The first number")] int a,
        [Description("The second number")] int b)
    {
        return a - b;
    }

    [KernelFunction]
    [Description("Multiplies two numbers")]
    public int Multiply(
        [Description("The first number")] int a,
        [Description("The second number")] int b)
    {
        return a * b;
    }

    [KernelFunction]
    [Description("Divides the first number by the second")]
    public double Divide(
        [Description("The dividend")] double a,
        [Description("The divisor")] double b)
    {
        if (b == 0)
            throw new ArgumentException("Cannot divide by zero");
        return a / b;
    }

    [KernelFunction]
    [Description("Calculates the factorial of a number")]
    public long Factorial([Description("The number to calculate factorial for")] int n)
    {
        if (n < 0)
            throw new ArgumentException("Factorial is not defined for negative numbers");
        
        if (n == 0 || n == 1)
            return 1;
        
        long result = 1;
        for (int i = 2; i <= n; i++)
        {
            result *= i;
        }
        return result;
    }

    [KernelFunction]
    [Description("Calculates the power of a number")]
    public double Power(
        [Description("The base number")] double baseNumber,
        [Description("The exponent")] double exponent)
    {
        return Math.Pow(baseNumber, exponent);
    }

    [KernelFunction]
    [Description("Calculates the square root of a number")]
    public double SquareRoot([Description("The number to calculate square root for")] double number)
    {
        if (number < 0)
            throw new ArgumentException("Cannot calculate square root of negative number");
        return Math.Sqrt(number);
    }

    [KernelFunction]
    [Description("Checks if a number is prime")]
    public bool IsPrime([Description("The number to check")] int number)
    {
        if (number < 2) return false;
        if (number == 2) return true;
        if (number % 2 == 0) return false;

        for (int i = 3; i <= Math.Sqrt(number); i += 2)
        {
            if (number % i == 0) return false;
        }
        return true;
    }

    [KernelFunction]
    [Description("Generates a random number between min and max (inclusive)")]
    public int RandomNumber(
        [Description("The minimum value")] int min,
        [Description("The maximum value")] int max)
    {
        var random = new Random();
        return random.Next(min, max + 1);
    }
}
