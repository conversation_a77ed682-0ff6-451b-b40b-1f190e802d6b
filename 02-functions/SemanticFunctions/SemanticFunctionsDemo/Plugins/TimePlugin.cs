using System.ComponentModel;
using Microsoft.SemanticKernel;

namespace SemanticFunctionsDemo;

/// <summary>
/// A plugin that provides date and time related functions.
/// </summary>
public class TimePlugin
{
    [KernelFunction]
    [Description("Gets the current date and time")]
    public string GetCurrentTime()
    {
        return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }

    [KernelFunction]
    [Description("Gets the current date")]
    public string GetCurrentDate()
    {
        return DateTime.Now.ToString("yyyy-MM-dd");
    }

    [KernelFunction]
    [Description("Gets the current time")]
    public string GetCurrentTimeOnly()
    {
        return DateTime.Now.ToString("HH:mm:ss");
    }

    [KernelFunction]
    [Description("Gets the current UTC time")]
    public string GetCurrentUtcTime()
    {
        return DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
    }

    [KernelFunction]
    [Description("Adds days to the current date")]
    public string AddDays([Description("Number of days to add")] int days)
    {
        return DateTime.Now.AddDays(days).ToString("yyyy-MM-dd");
    }

    [KernelFunction]
    [Description("Adds hours to the current time")]
    public string AddHours([Description("Number of hours to add")] int hours)
    {
        return DateTime.Now.AddHours(hours).ToString("yyyy-MM-dd HH:mm:ss");
    }

    [KernelFunction]
    [Description("Adds minutes to the current time")]
    public string AddMinutes([Description("Number of minutes to add")] int minutes)
    {
        return DateTime.Now.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss");
    }

    [KernelFunction]
    [Description("Gets the day of the week for the current date")]
    public string GetDayOfWeek()
    {
        return DateTime.Now.DayOfWeek.ToString();
    }

    [KernelFunction]
    [Description("Gets the day of the week for a specific date")]
    public string GetDayOfWeekForDate([Description("Date in yyyy-MM-dd format")] string date)
    {
        if (DateTime.TryParse(date, out DateTime parsedDate))
        {
            return parsedDate.DayOfWeek.ToString();
        }
        return "Invalid date format";
    }

    [KernelFunction]
    [Description("Calculates the difference in days between two dates")]
    public int DaysBetween(
        [Description("Start date in yyyy-MM-dd format")] string startDate,
        [Description("End date in yyyy-MM-dd format")] string endDate)
    {
        if (DateTime.TryParse(startDate, out DateTime start) && DateTime.TryParse(endDate, out DateTime end))
        {
            return (int)(end - start).TotalDays;
        }
        throw new ArgumentException("Invalid date format");
    }

    [KernelFunction]
    [Description("Formats a date string")]
    public string FormatDate(
        [Description("Date to format")] string date,
        [Description("Format string (e.g., 'MM/dd/yyyy', 'dd-MM-yyyy')")] string format = "yyyy-MM-dd")
    {
        if (DateTime.TryParse(date, out DateTime parsedDate))
        {
            return parsedDate.ToString(format);
        }
        return "Invalid date format";
    }

    [KernelFunction]
    [Description("Checks if a year is a leap year")]
    public bool IsLeapYear([Description("The year to check")] int year)
    {
        return DateTime.IsLeapYear(year);
    }

    [KernelFunction]
    [Description("Gets the number of days in a specific month and year")]
    public int DaysInMonth(
        [Description("The year")] int year,
        [Description("The month (1-12)")] int month)
    {
        if (month < 1 || month > 12)
            throw new ArgumentException("Month must be between 1 and 12");
        
        return DateTime.DaysInMonth(year, month);
    }

    [KernelFunction]
    [Description("Gets the current timestamp in Unix format")]
    public long GetUnixTimestamp()
    {
        return ((DateTimeOffset)DateTime.UtcNow).ToUnixTimeSeconds();
    }

    [KernelFunction]
    [Description("Converts Unix timestamp to readable date")]
    public string UnixToDate([Description("Unix timestamp")] long timestamp)
    {
        var dateTime = DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        return dateTime.ToString("yyyy-MM-dd HH:mm:ss UTC");
    }

    [KernelFunction]
    [Description("Gets the start of the current week (Monday)")]
    public string GetStartOfWeek()
    {
        var today = DateTime.Now;
        var daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
        if (daysFromMonday < 0) daysFromMonday += 7;
        
        var monday = today.AddDays(-daysFromMonday);
        return monday.ToString("yyyy-MM-dd");
    }

    [KernelFunction]
    [Description("Gets the end of the current week (Sunday)")]
    public string GetEndOfWeek()
    {
        var today = DateTime.Now;
        var daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
        if (daysFromMonday < 0) daysFromMonday += 7;
        
        var sunday = today.AddDays(6 - daysFromMonday);
        return sunday.ToString("yyyy-MM-dd");
    }
}
