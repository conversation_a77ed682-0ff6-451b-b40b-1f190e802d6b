using System.ComponentModel;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.SemanticKernel;

namespace SemanticFunctionsDemo;

/// <summary>
/// A plugin that provides text manipulation and analysis functions.
/// </summary>
public class TextPlugin
{
    [KernelFunction]
    [Description("Converts text to uppercase")]
    public string ToUpper([Description("The text to convert")] string text)
    {
        return text?.ToUpper() ?? string.Empty;
    }

    [KernelFunction]
    [Description("Converts text to lowercase")]
    public string ToLower([Description("The text to convert")] string text)
    {
        return text?.ToLower() ?? string.Empty;
    }

    [KernelFunction]
    [Description("Converts text to title case")]
    public string ToTitleCase([Description("The text to convert")] string text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var result = new StringBuilder();

        foreach (var word in words)
        {
            if (word.Length > 0)
            {
                result.Append(char.ToUpper(word[0]));
                if (word.Length > 1)
                    result.Append(word.Substring(1).ToLower());
                result.Append(' ');
            }
        }

        return result.ToString().TrimEnd();
    }

    [KernelFunction]
    [Description("Counts the number of words in text")]
    public int WordCount([Description("The text to count words in")] string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return 0;

        return text.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
    }

    [KernelFunction]
    [Description("Counts the number of characters in text")]
    public int CharacterCount([Description("The text to count characters in")] string text)
    {
        return text?.Length ?? 0;
    }

    [KernelFunction]
    [Description("Reverses the text")]
    public string Reverse([Description("The text to reverse")] string text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        char[] chars = text.ToCharArray();
        Array.Reverse(chars);
        return new string(chars);
    }

    [KernelFunction]
    [Description("Removes extra whitespace from text")]
    public string TrimWhitespace([Description("The text to trim")] string text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return Regex.Replace(text.Trim(), @"\s+", " ");
    }

    [KernelFunction]
    [Description("Checks if text contains a specific substring")]
    public bool Contains(
        [Description("The text to search in")] string text,
        [Description("The substring to search for")] string substring,
        [Description("Whether the search should be case sensitive")] bool caseSensitive = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(substring))
            return false;

        var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
        return text.Contains(substring, comparison);
    }

    [KernelFunction]
    [Description("Replaces all occurrences of a substring with another string")]
    public string Replace(
        [Description("The original text")] string text,
        [Description("The substring to replace")] string oldValue,
        [Description("The replacement string")] string newValue,
        [Description("Whether the replacement should be case sensitive")] bool caseSensitive = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(oldValue))
            return text ?? string.Empty;

        if (caseSensitive)
        {
            return text.Replace(oldValue, newValue ?? string.Empty);
        }
        else
        {
            return Regex.Replace(text, Regex.Escape(oldValue), newValue ?? string.Empty, RegexOptions.IgnoreCase);
        }
    }

    [KernelFunction]
    [Description("Extracts a substring from text")]
    public string Substring(
        [Description("The original text")] string text,
        [Description("The starting index")] int startIndex,
        [Description("The length of the substring (optional)")] int? length = null)
    {
        if (string.IsNullOrEmpty(text) || startIndex < 0 || startIndex >= text.Length)
            return string.Empty;

        if (length.HasValue)
        {
            var actualLength = Math.Min(length.Value, text.Length - startIndex);
            return actualLength > 0 ? text.Substring(startIndex, actualLength) : string.Empty;
        }

        return text.Substring(startIndex);
    }

    [KernelFunction]
    [Description("Splits text into an array of strings")]
    public string[] Split(
        [Description("The text to split")] string text,
        [Description("The delimiter to split on")] string delimiter = " ")
    {
        if (string.IsNullOrEmpty(text))
            return Array.Empty<string>();

        return text.Split(new[] { delimiter }, StringSplitOptions.RemoveEmptyEntries);
    }

    [KernelFunction]
    [Description("Joins an array of strings with a delimiter")]
    public string Join(
        [Description("The strings to join")] string[] strings,
        [Description("The delimiter to use")] string delimiter = " ")
    {
        return string.Join(delimiter, strings ?? Array.Empty<string>());
    }
}
