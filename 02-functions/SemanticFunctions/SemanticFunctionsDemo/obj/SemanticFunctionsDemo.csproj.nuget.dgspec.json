{"format": 1, "restore": {"/Users/<USER>/MSK/02-functions/SemanticFunctions/SemanticFunctionsDemo/SemanticFunctionsDemo.csproj": {}}, "projects": {"/Users/<USER>/MSK/02-functions/SemanticFunctions/SemanticFunctionsDemo/SemanticFunctionsDemo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/MSK/02-functions/SemanticFunctions/SemanticFunctionsDemo/SemanticFunctionsDemo.csproj", "projectName": "SemanticFunctionsDemo", "projectPath": "/Users/<USER>/MSK/02-functions/SemanticFunctions/SemanticFunctionsDemo/SemanticFunctionsDemo.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/MSK/02-functions/SemanticFunctions/SemanticFunctionsDemo/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.58.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}