﻿using Microsoft.Extensions.Configuration;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.ComponentModel;

namespace SemanticFunctionsDemo;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔧 Semantic Functions & Plugins Demo");
        Console.WriteLine("====================================\n");

        // Load configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();

        // Create kernel
        var kernel = CreateKernel(configuration);

        // Register plugins
        kernel.ImportPluginFromType<MathPlugin>("Math");
        kernel.ImportPluginFromType<TextPlugin>("Text");
        kernel.ImportPluginFromType<TimePlugin>("Time");

        // Run examples
        await RunSemanticFunctionExamples(kernel);
        await RunNativeFunctionExamples(kernel);
        await RunPluginExamples(kernel);
        await RunAdvancedExamples(kernel);

        Console.WriteLine("\n🎉 Functions and Plugins demo completed!");
        Console.WriteLine("Next: Explore 03-orchestration for agent patterns.");
    }

    private static Kernel CreateKernel(IConfiguration configuration)
    {
        var builder = Kernel.CreateBuilder();

        var openAiApiKey = configuration["OpenAI:ApiKey"];
        var azureEndpoint = configuration["AzureOpenAI:Endpoint"];

        if (!string.IsNullOrEmpty(openAiApiKey) && openAiApiKey != "YOUR_OPENAI_API_KEY_HERE")
        {
            Console.WriteLine("🔗 Using OpenAI");
            builder.AddOpenAIChatCompletion(
                modelId: configuration["OpenAI:ModelId"] ?? "gpt-4o-mini",
                apiKey: openAiApiKey);
        }
        else if (!string.IsNullOrEmpty(azureEndpoint) && azureEndpoint != "YOUR_AZURE_OPENAI_ENDPOINT_HERE")
        {
            Console.WriteLine("🔗 Using Azure OpenAI");
            builder.AddAzureOpenAIChatCompletion(
                deploymentName: configuration["AzureOpenAI:DeploymentName"]!,
                endpoint: azureEndpoint,
                apiKey: configuration["AzureOpenAI:ApiKey"]!);
        }
        else
        {
            throw new InvalidOperationException(
                "Please configure either OpenAI or Azure OpenAI settings in appsettings.json");
        }

        return builder.Build();
    }

    private static async Task RunSemanticFunctionExamples(Kernel kernel)
    {
        Console.WriteLine("📝 Semantic Functions Examples");
        Console.WriteLine("------------------------------");

        // Example 1: Simple semantic function
        var summarizeFunction = kernel.CreateFunctionFromPrompt(
            "Summarize the following text in 2-3 sentences: {{$input}}",
            functionName: "Summarize",
            description: "Summarizes text content");

        var longText = """
            Artificial Intelligence (AI) has revolutionized numerous industries and aspects of daily life.
            From healthcare diagnostics to autonomous vehicles, AI systems are becoming increasingly
            sophisticated and capable. Machine learning algorithms can now process vast amounts of data
            to identify patterns and make predictions with remarkable accuracy. However, the rapid
            advancement of AI also raises important questions about ethics, privacy, and the future
            of human employment. As we continue to develop these technologies, it's crucial to ensure
            they are designed and deployed responsibly.
            """;

        var summary = await kernel.InvokeAsync(summarizeFunction, new() { ["input"] = longText });
        Console.WriteLine($"Summary: {summary}");

        // Example 2: Semantic function with multiple parameters
        var translateFunction = kernel.CreateFunctionFromPrompt(
            """
            Translate the following {{$sourceLanguage}} text to {{$targetLanguage}}:

            {{$text}}

            Provide only the translation without any additional commentary.
            """,
            functionName: "Translate",
            description: "Translates text between languages");

        var translation = await kernel.InvokeAsync(translateFunction, new()
        {
            ["sourceLanguage"] = "English",
            ["targetLanguage"] = "Spanish",
            ["text"] = "Hello, how are you today?"
        });
        Console.WriteLine($"Translation: {translation}");
    }

    private static async Task RunNativeFunctionExamples(Kernel kernel)
    {
        Console.WriteLine("\n🔧 Native Functions Examples");
        Console.WriteLine("----------------------------");

        // Example 1: Simple native function
        var getCurrentWeather = kernel.CreateFunctionFromMethod(
            (string location) => $"The weather in {location} is sunny with 72°F",
            "GetWeather",
            "Gets the current weather for a location");

        var weather = await kernel.InvokeAsync(getCurrentWeather, new() { ["location"] = "Seattle" });
        Console.WriteLine($"Weather: {weather}");

        // Example 2: Native function with complex logic
        var calculateAge = kernel.CreateFunctionFromMethod(
            (DateTime birthDate) =>
            {
                var today = DateTime.Today;
                var age = today.Year - birthDate.Year;
                if (birthDate.Date > today.AddYears(-age)) age--;
                return $"Age: {age} years old";
            },
            "CalculateAge",
            "Calculates age from birth date");

        var age = await kernel.InvokeAsync(calculateAge, new() { ["birthDate"] = new DateTime(1990, 5, 15) });
        Console.WriteLine($"Age calculation: {age}");
    }

    private static async Task RunPluginExamples(Kernel kernel)
    {
        Console.WriteLine("\n🔌 Plugin Examples");
        Console.WriteLine("------------------");

        // Math plugin examples
        var addResult = await kernel.InvokeAsync("Math", "Add", new() { ["a"] = "15", ["b"] = "25" });
        Console.WriteLine($"Math.Add(15, 25) = {addResult}");

        var factorialResult = await kernel.InvokeAsync("Math", "Factorial", new() { ["n"] = "5" });
        Console.WriteLine($"Math.Factorial(5) = {factorialResult}");

        // Text plugin examples
        var upperResult = await kernel.InvokeAsync("Text", "ToUpper", new() { ["text"] = "hello world" });
        Console.WriteLine($"Text.ToUpper('hello world') = {upperResult}");

        var wordCountResult = await kernel.InvokeAsync("Text", "WordCount", new() { ["text"] = "The quick brown fox jumps" });
        Console.WriteLine($"Text.WordCount('The quick brown fox jumps') = {wordCountResult}");

        // Time plugin examples
        var currentTimeResult = await kernel.InvokeAsync("Time", "GetCurrentTime");
        Console.WriteLine($"Time.GetCurrentTime() = {currentTimeResult}");

        var addDaysResult = await kernel.InvokeAsync("Time", "AddDays", new() { ["days"] = "7" });
        Console.WriteLine($"Time.AddDays(7) = {addDaysResult}");
    }

    private static async Task RunAdvancedExamples(Kernel kernel)
    {
        Console.WriteLine("\n🚀 Advanced Function Composition");
        Console.WriteLine("--------------------------------");

        // Combine semantic and native functions
        var analysisPrompt = """
            Analyze the following mathematical result and provide insights:

            Result: {{$mathResult}}
            Operation: {{$operation}}

            Provide a brief analysis of what this result means and any interesting mathematical properties.
            """;

        var analysisFunction = kernel.CreateFunctionFromPrompt(analysisPrompt, "AnalyzeMath");

        // First, perform a calculation
        var calculation = await kernel.InvokeAsync("Math", "Factorial", new() { ["n"] = "6" });

        // Then analyze the result
        var analysis = await kernel.InvokeAsync(analysisFunction, new()
        {
            ["mathResult"] = calculation.ToString(),
            ["operation"] = "factorial of 6"
        });

        Console.WriteLine($"Mathematical Analysis: {analysis}");

        // Chain multiple functions
        var text = "SEMANTIC KERNEL IS AMAZING FOR AI DEVELOPMENT";
        var lowerText = await kernel.InvokeAsync("Text", "ToLower", new() { ["text"] = text });
        var wordCount = await kernel.InvokeAsync("Text", "WordCount", new() { ["text"] = lowerText.ToString() });

        Console.WriteLine($"Original: {text}");
        Console.WriteLine($"Lowercase: {lowerText}");
        Console.WriteLine($"Word count: {wordCount}");
    }
}
