# Getting Started with Semantic Kernel Learning Project

This guide will help you set up and start learning Semantic Kernel and AI agent orchestration from scratch.

## 🚀 Quick Start (5 minutes)

### 1. Prerequisites Check
Ensure you have:
- .NET 8.0 SDK installed
- A code editor (VS Code recommended)
- An OpenAI API key OR Azure OpenAI access

### 2. <PERSON><PERSON> and Setup
```bash
# Clone the repository
git clone <repository-url>
cd SemanticKernelLearning

# Restore all dependencies
dotnet restore

# Build the solution
dotnet build
```

### 3. Configure AI Service
Choose your AI service and configure:

**Option A: OpenAI**
```bash
cd 01-basics/HelloSemanticKernel/HelloSemanticKernel
# Edit appsettings.json and add your OpenAI API key
```

**Option B: Azure OpenAI**
```bash
cd 01-basics/HelloSemanticKernel/HelloSemanticKernel
# Edit appsettings.json and add your Azure OpenAI details
```

### 4. Run Your First Example
```bash
dotnet run
```

If you see AI responses, you're ready to start learning! 🎉

## 📋 Detailed Setup Guide

### Step 1: Install Prerequisites

#### .NET 8.0 SDK
**Windows**:
- Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
- Run the installer
- Verify: `dotnet --version`

**macOS**:
```bash
# Using Homebrew
brew install dotnet

# Or download from Microsoft
# Verify installation
dotnet --version
```

**Linux (Ubuntu/Debian)**:
```bash
# Add Microsoft package repository
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb

# Install .NET SDK
sudo apt-get update
sudo apt-get install -y dotnet-sdk-8.0

# Verify installation
dotnet --version
```

#### Code Editor
**Recommended: Visual Studio Code**
- Download from [VS Code](https://code.visualstudio.com/)
- Install C# extension
- Install .NET extension pack

**Alternative: Visual Studio 2022**
- Download Community edition (free)
- Include .NET workload during installation

### Step 2: AI Service Setup

#### Option A: OpenAI Setup
1. **Create Account**:
   - Go to [OpenAI Platform](https://platform.openai.com/)
   - Sign up for an account
   - Verify your email

2. **Get API Key**:
   - Navigate to API Keys section
   - Click "Create new secret key"
   - Copy and save the key securely
   - **Important**: Never commit API keys to version control

3. **Set Up Billing**:
   - Add payment method in billing section
   - Set usage limits to control costs
   - Start with a small limit ($5-10) for learning

4. **Test API Access**:
   ```bash
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer YOUR_API_KEY"
   ```

#### Option B: Azure OpenAI Setup
1. **Azure Subscription**:
   - Create Azure account if needed
   - Ensure you have an active subscription

2. **Create Azure OpenAI Resource**:
   - Go to Azure Portal
   - Search for "Azure OpenAI"
   - Create new resource
   - Choose appropriate region and pricing tier

3. **Deploy Model**:
   - In your Azure OpenAI resource
   - Go to "Model deployments"
   - Deploy GPT-4 or GPT-3.5-turbo
   - Note the deployment name

4. **Get Connection Details**:
   - Endpoint URL (from resource overview)
   - API Key (from Keys and Endpoint section)
   - Deployment name (from model deployments)

### Step 3: Project Configuration

#### Download/Clone Project
```bash
# If using Git
git clone <repository-url>
cd SemanticKernelLearning

# If downloading ZIP
# Extract to desired location
cd SemanticKernelLearning
```

#### Configure AI Service
Navigate to the first example:
```bash
cd 01-basics/HelloSemanticKernel/HelloSemanticKernel
```

Edit `appsettings.json`:

**For OpenAI**:
```json
{
  "OpenAI": {
    "ApiKey": "sk-your-actual-api-key-here",
    "ModelId": "gpt-4o-mini"
  },
  "AzureOpenAI": {
    "Endpoint": "YOUR_AZURE_OPENAI_ENDPOINT_HERE",
    "ApiKey": "YOUR_AZURE_OPENAI_API_KEY_HERE",
    "DeploymentName": "YOUR_DEPLOYMENT_NAME_HERE"
  }
}
```

**For Azure OpenAI**:
```json
{
  "OpenAI": {
    "ApiKey": "YOUR_OPENAI_API_KEY_HERE",
    "ModelId": "gpt-4o-mini"
  },
  "AzureOpenAI": {
    "Endpoint": "https://your-resource.openai.azure.com/",
    "ApiKey": "your-azure-api-key",
    "DeploymentName": "your-deployment-name"
  }
}
```

#### Environment Variables (Alternative)
Instead of editing files, you can use environment variables:

**Windows (PowerShell)**:
```powershell
$env:OpenAI__ApiKey = "your-api-key"
$env:OpenAI__ModelId = "gpt-4o-mini"
```

**macOS/Linux (Bash)**:
```bash
export OpenAI__ApiKey="your-api-key"
export OpenAI__ModelId="gpt-4o-mini"
```

### Step 4: Verify Setup

#### Build the Solution
```bash
# From the root directory
dotnet restore
dotnet build
```

You should see:
```
Build succeeded.
    0 Warning(s)
    0 Error(s)
```

#### Run First Example
```bash
cd 01-basics/HelloSemanticKernel/HelloSemanticKernel
dotnet run
```

Expected output:
```
🤖 Welcome to Semantic Kernel Learning!
=====================================

🔗 Using OpenAI
📝 Example 1: Simple AI Chat
----------------------------
AI: Semantic Kernel is a lightweight SDK that enables integration of AI Large Language Models with conventional programming languages.

📝 Example 2: Prompt with Variables
-----------------------------------
AI: Dependency injection in C# is a design pattern that allows you to provide dependencies to a class from external sources rather than creating them internally...

📝 Example 3: Function Calling
------------------------------
AI: The current time is 2024-01-15 14:30:22. It's a beautiful Monday afternoon!

🎉 Basic Semantic Kernel examples completed!
Next: Explore 02-functions for more advanced capabilities.
```

## 🛠️ Troubleshooting

### Common Issues

#### "API key not configured" Error
**Problem**: AI service not properly configured
**Solution**:
1. Check `appsettings.json` has correct API key
2. Ensure no placeholder text remains
3. Verify API key is valid
4. Check environment variables if using them

#### "Unauthorized" or "Invalid API Key" Error
**Problem**: API key issues
**Solution**:
1. Verify API key is correct (no extra spaces)
2. Check API key permissions
3. Ensure billing is set up (for OpenAI)
4. Try regenerating the API key

#### "Model not found" Error
**Problem**: Model or deployment name incorrect
**Solution**:
1. Check model name spelling
2. Verify deployment exists (Azure OpenAI)
3. Ensure model is available in your region
4. Try a different model (e.g., gpt-3.5-turbo)

#### Build Errors
**Problem**: Missing dependencies or .NET issues
**Solution**:
1. Run `dotnet restore` again
2. Check .NET version: `dotnet --version`
3. Clear NuGet cache: `dotnet nuget locals all --clear`
4. Rebuild: `dotnet clean && dotnet build`

#### Network/Firewall Issues
**Problem**: Cannot reach AI service
**Solution**:
1. Check internet connection
2. Verify firewall allows HTTPS traffic
3. Try from different network
4. Check corporate proxy settings

### Getting Help

1. **Check Error Messages**: Read the full error message carefully
2. **Review Configuration**: Double-check all settings
3. **Test Connectivity**: Try a simple curl command to test API access
4. **Search Documentation**: Look for similar issues in the docs
5. **Ask for Help**: Use community forums or GitHub issues

## 📚 What's Next?

### Immediate Next Steps
1. **Explore the Examples**: Run all examples in `01-basics`
2. **Read the Code**: Understand how each example works
3. **Make Modifications**: Try changing prompts and parameters
4. **Move to Functions**: Progress to `02-functions` when ready

### Learning Path
1. **Week 1**: Complete `01-basics` and `02-functions`
2. **Week 2**: Work through `03-orchestration`
3. **Week 3**: Tackle `04-advanced`
4. **Week 4**: Build your own project using learned concepts

### Resources
- **Learning Guide**: `docs/LEARNING_GUIDE.md`
- **Module READMEs**: Each module has detailed documentation
- **Official Docs**: [Microsoft Semantic Kernel Documentation](https://learn.microsoft.com/en-us/semantic-kernel/)

## 🎯 Success Criteria

You're ready to proceed when you can:
- [ ] Successfully run the basic example
- [ ] See AI responses in the console
- [ ] Understand the basic code structure
- [ ] Modify prompts and see different responses

Congratulations! You're now ready to dive deep into Semantic Kernel and AI agent orchestration! 🚀
