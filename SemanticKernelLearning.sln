﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-basics", "01-basics", "{0ABBB428-5377-4319-9DFC-2B211EA23708}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "HelloSemanticKernel", "HelloSemanticKernel", "{34F539F1-3B45-4FCD-BA11-5448ADDA161F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HelloSemanticKernel", "01-basics\HelloSemanticKernel\HelloSemanticKernel\HelloSemanticKernel.csproj", "{C995E91F-A4F5-4305-95B0-212C52E2644D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-functions", "02-functions", "{D228EC32-623B-4C28-AF5B-D55B44DB213D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SemanticFunctions", "SemanticFunctions", "{470B29F8-D6D5-4164-AD33-2F40BF8434CA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SemanticFunctionsDemo", "02-functions\SemanticFunctions\SemanticFunctionsDemo\SemanticFunctionsDemo.csproj", "{F2B412EE-6369-459A-ADC5-1B4C8E2FA7B1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-orchestration", "03-orchestration", "{C5C47BBC-A3B4-424B-AD44-A14DB61B2D6B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SingleAgent", "SingleAgent", "{32908F01-E18E-4366-9E30-754055EB0585}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SingleAgentDemo", "03-orchestration\SingleAgent\SingleAgentDemo\SingleAgentDemo.csproj", "{B73F93AF-AA30-4095-BBA6-DB7E41EECF67}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-advanced", "04-advanced", "{898F323E-0C52-4C47-89A7-29D537FBA971}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Planning", "Planning", "{520D5008-47AB-4A02-8832-5234652A4D85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PlanningDemo", "04-advanced\Planning\PlanningDemo\PlanningDemo.csproj", "{C1F169AD-716F-45E7-BFF1-410F9108DB78}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C995E91F-A4F5-4305-95B0-212C52E2644D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C995E91F-A4F5-4305-95B0-212C52E2644D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C995E91F-A4F5-4305-95B0-212C52E2644D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C995E91F-A4F5-4305-95B0-212C52E2644D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2B412EE-6369-459A-ADC5-1B4C8E2FA7B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2B412EE-6369-459A-ADC5-1B4C8E2FA7B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2B412EE-6369-459A-ADC5-1B4C8E2FA7B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2B412EE-6369-459A-ADC5-1B4C8E2FA7B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{B73F93AF-AA30-4095-BBA6-DB7E41EECF67}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B73F93AF-AA30-4095-BBA6-DB7E41EECF67}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B73F93AF-AA30-4095-BBA6-DB7E41EECF67}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B73F93AF-AA30-4095-BBA6-DB7E41EECF67}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1F169AD-716F-45E7-BFF1-410F9108DB78}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1F169AD-716F-45E7-BFF1-410F9108DB78}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1F169AD-716F-45E7-BFF1-410F9108DB78}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1F169AD-716F-45E7-BFF1-410F9108DB78}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{34F539F1-3B45-4FCD-BA11-5448ADDA161F} = {0ABBB428-5377-4319-9DFC-2B211EA23708}
		{C995E91F-A4F5-4305-95B0-212C52E2644D} = {34F539F1-3B45-4FCD-BA11-5448ADDA161F}
		{470B29F8-D6D5-4164-AD33-2F40BF8434CA} = {D228EC32-623B-4C28-AF5B-D55B44DB213D}
		{F2B412EE-6369-459A-ADC5-1B4C8E2FA7B1} = {470B29F8-D6D5-4164-AD33-2F40BF8434CA}
		{32908F01-E18E-4366-9E30-754055EB0585} = {C5C47BBC-A3B4-424B-AD44-A14DB61B2D6B}
		{B73F93AF-AA30-4095-BBA6-DB7E41EECF67} = {32908F01-E18E-4366-9E30-754055EB0585}
		{520D5008-47AB-4A02-8832-5234652A4D85} = {898F323E-0C52-4C47-89A7-29D537FBA971}
		{C1F169AD-716F-45E7-BFF1-410F9108DB78} = {520D5008-47AB-4A02-8832-5234652A4D85}
	EndGlobalSection
EndGlobal
